import express from 'express';
import cors from 'cors';
import helmet from 'helmet'; // Importa helmet para headers de segurança
import rateLimit from 'express-rate-limit';
import session from 'express-session'; // Importa express-session para CSRF
import cookieParser from 'cookie-parser'; // Importa cookie-parser para cookies
import dotenv from 'dotenv';
import prismaService from './services/prismaService.js';
import redisService from './services/redisService.js';
import githubCacheService from './services/githubCacheService.js';
import metricsService from './services/metricsService.js';
import { swaggerSpec, swaggerUi, swaggerUiOptions } from './config/swagger.js';
import { metricsMiddleware } from './middleware/metrics.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting INTELIGENTE - diferente para produção e testes
const isTestEnvironment = process.env.NODE_ENV === 'test' || process.env.TESTING === 'true';

// Função para normalizar IPs e resolver problemas com IPv6
const normalizeIP = (req) => {
  let ip = req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress || '127.0.0.1';

  // Normalizar IPv6 mapped IPv4 addresses
  if (ip.startsWith('::ffff:')) {
    ip = ip.substring(7);
  }

  // Normalizar localhost IPv6
  if (ip === '::1') {
    ip = '127.0.0.1';
  }

  // Garantir que temos um IP válido
  if (!ip || ip === '::' || ip.includes('::')) {
    ip = '127.0.0.1';
  }

  return ip;
};

// Rate limiting global - mais permissivo para testes
const limiter = rateLimit({
  windowMs: isTestEnvironment ? 1 * 60 * 1000 : 15 * 60 * 1000, // 1min para testes, 15min para produção
  max: isTestEnvironment ? 1000 : 100, // 1000 para testes, 100 para produção
  message: {
    error: 'Muitas requisições deste IP, tente novamente em alguns minutos',
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip para User-Agents de teste específicos
  skip: (req) => {
    if (isTestEnvironment) return false; // Em ambiente de teste, aplicar rate limiting normal

    // Skip apenas para ferramentas de monitoramento em produção
    const monitoringAgents = ['HealthCheck', 'Monitoring', 'Uptime'];
    return monitoringAgents.some(agent => req.get('User-Agent')?.includes(agent));
  },
  // Usar função personalizada para gerar chave e evitar problemas IPv6
  keyGenerator: (req) => {
    return normalizeIP(req);
  }
});

// Rate limiting específico para autenticação - SUBSTITUÍDO POR userRateLimiters.auth
// const authLimiter = rateLimit({ ... }) - REMOVIDO

// Rate limiting específico para logout (menos restritivo)
const logoutLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: isTestEnvironment ? 200 : 50, // 200 para testes, 50 para produção
  message: {
    error: 'Muitas tentativas de logout, tente novamente em 15 minutos',
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Usar função personalizada para gerar chave e evitar problemas IPv6
  keyGenerator: (req) => {
    return normalizeIP(req);
  }
});

// Rate limiting para GitHub - SUBSTITUÍDO POR userRateLimiters.api
// const githubLimiter = rateLimit({ ... }) - REMOVIDO
// const githubDetailsLimiter = rateLimit({ ... }) - REMOVIDO

// Middleware de segurança com helmet
app.use(
  helmet({
    // Configurações específicas para nossa aplicação
    contentSecurityPolicy: process.env.NODE_ENV === 'production' ? {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"], // Permite CSS inline para Tailwind
        scriptSrc: ["'self'", "'unsafe-eval'", "'unsafe-inline'"], // Necessário para Vite em desenvolvimento
        imgSrc: ["'self'", 'data:', 'https:'], // Permite imagens de HTTPS
        connectSrc: [
          "'self'",
          'https://api.github.com',
          'https://generativelanguage.googleapis.com',
        ], // APIs que usaremos
        fontSrc: ["'self'", 'https://fonts.gstatic.com'], // Fontes do Google
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    } : false, // Desabilitar CSP em desenvolvimento
    // Outras configurações de segurança
    hsts: {
      maxAge: 31536000, // 1 ano
      includeSubDomains: true,
      preload: true,
    },
    noSniff: true, // Previne MIME sniffing
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  })
);

// Aplicar rate limiting global
app.use(limiter);

// Aplicar monitoramento de circuit breakers
app.use(monitoringMiddleware);

// Aplicar middleware de métricas
app.use(metricsMiddleware);

// Configurar sessões para CSRF
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'code2post-session-secret',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production', // HTTPS em produção
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 horas
      sameSite: 'strict',
    },
  })
);

// Configuração de CORS para múltiplas origens
const allowedOrigins = [
  'https://www.code2post.com',
  'https://code2post.com',
  'https://code2-post-ecwdmja4s-gabriel-camarates-projects.vercel.app',
  process.env.FRONTEND_URL || 'http://localhost:5173'
].filter(Boolean); // Remove valores undefined/null

// Middleware CORS melhorado para lidar com preflight requests e burst de requisições
app.use(
  cors({
    origin: function (origin, callback) {
      // Permitir requisições sem origin (mobile apps, etc.)
      if (!origin) return callback(null, true);

      // Em desenvolvimento, ser mais permissivo
      if (process.env.NODE_ENV !== 'production') {
        // Permitir localhost em qualquer porta
        if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
          return callback(null, true);
        }
      }

      // Verificar se a origin está na lista permitida
      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.log('❌ CORS blocked origin:', origin);
        console.log('✅ Allowed origins:', allowedOrigins);
        // Em desenvolvimento, permitir mesmo assim com warning
        if (process.env.NODE_ENV !== 'production') {
          console.log('⚠️ Permitindo origin em desenvolvimento');
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'x-github-token',
      'X-Requested-With',
      'Accept',
      'Origin',
      'Cache-Control',
      'Pragma'
    ],
    exposedHeaders: [
      'Content-Length',
      'X-Requested-With',
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset'
    ],
    maxAge: 86400, // Cache preflight por 24 horas
    optionsSuccessStatus: 200 // Para suporte a browsers legados
  })
);

// Middleware para handling de OPTIONS requests
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-github-token, X-Requested-With, Accept, Origin, Cache-Control, Pragma');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Max-Age', '86400');
  res.sendStatus(200);
});

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser()); // Middleware para parsing de cookies

// Servir arquivos estáticos para testes
app.use('/test', express.static('public'));

// Middleware de métricas (antes das rotas)
app.use(metricsMiddleware);

// Importar rotas
import authRoutes from './routes/auth.js';
// import simpleAuthRoutes from './routes/simple-auth.js'; // REMOVIDO - endpoints temporários
import registerRoutes from './routes/register.js';
import loginRoutes from './routes/login.js';
import githubRoutes from './routes/github.js';
import geminiRoutes from './routes/geminiRoutes.js';
import githubConfigRoutes from './routes/githubConfig.js';
import healthRoutes from './routes/health.js';
import testAuthRoutes from './routes/test-auth.js';

// Importar middleware CSRF
import {
  generateCSRFToken,
  validateCSRFToken,
  getCSRFToken,
} from './middleware/csrf.js';

// Importar Circuit Breakers e Retry Policies
import { monitoringMiddleware } from './middleware/circuitBreaker.js';
import userRateLimiters from './middleware/userRateLimit.js';

// Importar Response Cache Middleware
import responseCache from './middleware/responseCache.js';

// Routes (com cache para reduzir latência)
app.get('/', responseCache.create({ ttl: 300 }), (req, res) => {
  res.json({
    message: 'Code2Post API is running!',
    version: '1.0.0',
    status: 'active',
  });
});

// Rota específica para logout com rate limiting
app.post('/auth/logout', logoutLimiter, (req, res) => {
  // Limpar cookies de autenticação
  res.clearCookie('accessToken', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
  });

  res.clearCookie('refreshToken', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
  });

  res.json({ success: true, message: 'Logout realizado com sucesso' });
});

// Usar rota de registro com rate limiting permissivo para testes
app.use('/auth/register', registerRoutes);

// Usar rota de login com rate limiting permissivo para testes
app.use('/auth/login', loginRoutes);

// Rotas temporárias removidas - retornar 404 explícito ANTES da rota catch-all
app.post('/auth/simple-login', (req, res) => {
  res.status(404).json({
    error: 'Endpoint removido',
    message: 'Este endpoint temporário foi removido. Use /auth/login.',
    code: 'ENDPOINT_REMOVED'
  });
});

app.get('/auth/simple-verify', (req, res) => {
  res.status(404).json({
    error: 'Endpoint removido',
    message: 'Este endpoint temporário foi removido. Use /auth/verify.',
    code: 'ENDPOINT_REMOVED'
  });
});

// Usar rotas de autenticação com rate limiting por usuário e CSRF
app.use('/auth', userRateLimiters.general, validateCSRFToken, authRoutes);

// Usar rotas do GitHub com rate limiting por usuário
app.use('/auth/github', userRateLimiters.api, githubRoutes);

// Usar rotas de configuração do GitHub com rate limiting por usuário e cache
app.use('/api/github', userRateLimiters.api, responseCache.repositories(), githubConfigRoutes);

// Usar rotas da Gemini API com CSRF
app.use('/api/gemini', validateCSRFToken, geminiRoutes);

// Rota para obter token CSRF (com cache)
app.get('/csrf-token', responseCache.create({ ttl: 3600 }), generateCSRFToken, getCSRFToken);

// Rotas de health e monitoramento (com cache para reduzir latência)
app.use('/health', responseCache.health(), healthRoutes);

// Health check endpoint removido - usando apenas o das rotas

// Cache stats endpoint
app.get('/cache/stats', async (req, res) => {
  try {
    const stats = await githubCacheService.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get cache stats',
      message: error.message,
    });
  }
});

/**
 * @swagger
 * /metrics:
 *   get:
 *     tags: [System]
 *     summary: Obter métricas de performance do sistema
 *     description: Retorna métricas detalhadas de performance, incluindo tempo de resposta, cache hit rate, e estatísticas de uso
 *     responses:
 *       200:
 *         description: Métricas obtidas com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Metrics'
 *       500:
 *         description: Erro interno do servidor
 */
app.get('/metrics', async (req, res) => {
  try {
    const metrics = metricsService.getAllMetrics();
    res.json(metrics);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get metrics',
      message: error.message,
    });
  }
});

/**
 * @swagger
 * /metrics/prometheus:
 *   get:
 *     tags: [System]
 *     summary: Métricas no formato Prometheus
 *     description: Retorna métricas no formato compatível com Prometheus para monitoramento
 *     responses:
 *       200:
 *         description: Métricas no formato Prometheus
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: |
 *                 # HELP code2post_requests_total Total number of HTTP requests
 *                 # TYPE code2post_requests_total counter
 *                 code2post_requests_total 1234
 */
app.get('/metrics/prometheus', async (req, res) => {
  try {
    const prometheusMetrics = metricsService.exportPrometheusMetrics();
    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to export Prometheus metrics',
      message: error.message,
    });
  }
});

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message,
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
  });
});

// Inicializar Prisma, Redis e depois iniciar servidor
async function startServer() {
  try {
    // Inicializar conexão com banco de dados
    await prismaService.initialize();
    console.log('✅ Banco de dados conectado');

    // Inicializar Redis (opcional - continua sem Redis se falhar)
    try {
      await redisService.initialize();
      console.log('✅ Redis conectado');
    } catch (redisError) {
      console.warn('⚠️ Redis não disponível, continuando sem cache:', redisError.message);
    }

    // Iniciar servidor
    app.listen(PORT, () => {
      console.log(`🚀 Code2Post API running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`🗄️ Database: SQLite via Prisma`);
      console.log(`🔄 Cache: ${redisService.isAvailable() ? 'Redis' : 'Disabled'}`);

      // Monitoramento contínuo via middleware (metricsMiddleware já aplicado)
      console.log(`📊 Monitoramento contínuo ativo via middleware`);
    });
  } catch (error) {
    console.error('❌ Erro ao iniciar servidor:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🔄 Encerrando servidor...');
  await prismaService.disconnect();
  await redisService.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🔄 Encerrando servidor...');
  await prismaService.disconnect();
  await redisService.disconnect();
  process.exit(0);
});

startServer();

export default app;
