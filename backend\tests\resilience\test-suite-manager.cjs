/**
 * Gerenciador da Suíte de Testes de Resiliência
 * Coordena todos os testes de carga, stress e falha
 */

const fs = require('fs');
const path = require('path');

class ResilienceTestSuite {
  constructor() {
    this.results = [];
    this.startTime = null;
    this.endTime = null;
    this.reportDir = path.join(__dirname, 'reports');
    this.ensureReportDir();
  }

  ensureReportDir() {
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
  }

  async runAllTests() {
    console.log('🧪 INICIANDO SUÍTE DE TESTES DE RESILIÊNCIA');
    console.log('==========================================');
    console.log('');
    
    this.startTime = new Date();
    
    try {
      // 1. Testes de Carga
      console.log('📊 FASE 1: TESTES DE CARGA');
      console.log('---------------------------');
      await this.runLoadTests();
      console.log('');

      // 2. Testes de Stress
      console.log('⚡ FASE 2: TESTES DE STRESS');
      console.log('---------------------------');
      await this.runStressTests();
      console.log('');

      // 3. Testes de Falha
      console.log('💥 FASE 3: TESTES DE FALHA');
      console.log('---------------------------');
      await this.runFailureTests();
      console.log('');

      // 4. Testes de Recuperação
      console.log('🔄 FASE 4: TESTES DE RECUPERAÇÃO');
      console.log('--------------------------------');
      await this.runRecoveryTests();
      console.log('');

      // 5. Testes de Memory Leak
      console.log('🧠 FASE 5: TESTES DE MEMORY LEAK');
      console.log('--------------------------------');
      await this.runMemoryTests();
      console.log('');

      this.endTime = new Date();
      await this.generateReport();
      
    } catch (error) {
      console.error('❌ Erro na execução da suíte:', error.message);
      this.endTime = new Date();
      await this.generateErrorReport(error);
    }
  }

  async runLoadTests() {
    const tests = [
      { name: 'Carga Baixa', users: 10, duration: 60 },
      { name: 'Carga Média', users: 50, duration: 120 },
      { name: 'Carga Alta', users: 100, duration: 180 },
      { name: 'Carga Pico', users: 200, duration: 300 }
    ];

    for (const test of tests) {
      console.log(`🔍 Executando: ${test.name} (${test.users} usuários, ${test.duration}s)`);
      
      try {
        const result = await this.executeLoadTest(test);
        this.results.push({
          phase: 'Load',
          test: test.name,
          status: 'SUCCESS',
          metrics: result,
          timestamp: new Date()
        });
        
        console.log(`✅ ${test.name}: Concluído`);
        console.log(`   - RPS médio: ${result.avgRPS}`);
        console.log(`   - Latência média: ${result.avgLatency}ms`);
        console.log(`   - Taxa de erro: ${result.errorRate}%`);
        
      } catch (error) {
        console.log(`❌ ${test.name}: Falhou - ${error.message}`);
        this.results.push({
          phase: 'Load',
          test: test.name,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date()
        });
      }
      
      // Pausa entre testes para recuperação
      await this.sleep(30000);
    }
  }

  async runStressTests() {
    const tests = [
      { name: 'Stress CPU', type: 'cpu', intensity: 'high' },
      { name: 'Stress Memória', type: 'memory', intensity: 'high' },
      { name: 'Stress Conexões', type: 'connections', intensity: 'extreme' },
      { name: 'Stress Banco de Dados', type: 'database', intensity: 'high' }
    ];

    for (const test of tests) {
      console.log(`🔍 Executando: ${test.name}`);
      
      try {
        const result = await this.executeStressTest(test);
        this.results.push({
          phase: 'Stress',
          test: test.name,
          status: 'SUCCESS',
          metrics: result,
          timestamp: new Date()
        });
        
        console.log(`✅ ${test.name}: Sistema resistiu ao stress`);
        
      } catch (error) {
        console.log(`❌ ${test.name}: Sistema falhou - ${error.message}`);
        this.results.push({
          phase: 'Stress',
          test: test.name,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date()
        });
      }
      
      await this.sleep(60000); // Pausa maior para recuperação
    }
  }

  async runFailureTests() {
    const tests = [
      { name: 'Desconexão Banco', type: 'database_disconnect' },
      { name: 'Redis Indisponível', type: 'redis_down' },
      { name: 'Sobrecarga de Requests', type: 'request_flood' },
      { name: 'Corrupção de Dados', type: 'data_corruption' }
    ];

    for (const test of tests) {
      console.log(`🔍 Executando: ${test.name}`);
      
      try {
        const result = await this.executeFailureTest(test);
        this.results.push({
          phase: 'Failure',
          test: test.name,
          status: 'SUCCESS',
          metrics: result,
          timestamp: new Date()
        });
        
        console.log(`✅ ${test.name}: Sistema se recuperou adequadamente`);
        
      } catch (error) {
        console.log(`❌ ${test.name}: Falha crítica - ${error.message}`);
        this.results.push({
          phase: 'Failure',
          test: test.name,
          status: 'CRITICAL',
          error: error.message,
          timestamp: new Date()
        });
      }
      
      await this.sleep(45000);
    }
  }

  async runRecoveryTests() {
    console.log('🔍 Testando capacidade de recuperação automática...');
    
    try {
      const result = await this.executeRecoveryTest();
      this.results.push({
        phase: 'Recovery',
        test: 'Auto Recovery',
        status: 'SUCCESS',
        metrics: result,
        timestamp: new Date()
      });
      
      console.log('✅ Sistema demonstrou boa capacidade de recuperação');
      
    } catch (error) {
      console.log(`❌ Problemas na recuperação: ${error.message}`);
      this.results.push({
        phase: 'Recovery',
        test: 'Auto Recovery',
        status: 'FAILED',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  async runMemoryTests() {
    console.log('🔍 Monitorando vazamentos de memória...');
    
    try {
      const result = await this.executeMemoryTest();
      this.results.push({
        phase: 'Memory',
        test: 'Memory Leak Detection',
        status: result.leakDetected ? 'WARNING' : 'SUCCESS',
        metrics: result,
        timestamp: new Date()
      });
      
      if (result.leakDetected) {
        console.log('⚠️ Possível vazamento de memória detectado');
      } else {
        console.log('✅ Nenhum vazamento de memória detectado');
      }
      
    } catch (error) {
      console.log(`❌ Erro no teste de memória: ${error.message}`);
      this.results.push({
        phase: 'Memory',
        test: 'Memory Leak Detection',
        status: 'FAILED',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  // Métodos de execução dos testes
  async executeLoadTest(test) {
    return require('./load-test-executor.cjs').execute(test);
  }

  async executeStressTest(test) {
    return require('./stress-test-executor.cjs').execute(test);
  }

  async executeFailureTest(test) {
    // Implementação simplificada para testes de falha
    console.log(`🔍 Simulando falha: ${test.type}`);

    switch (test.type) {
      case 'database_disconnect':
        return await this.simulateDatabaseFailure();
      case 'redis_down':
        return await this.simulateRedisFailure();
      case 'request_flood':
        return await this.simulateRequestFlood();
      case 'data_corruption':
        return await this.simulateDataCorruption();
      default:
        throw new Error(`Tipo de falha desconhecido: ${test.type}`);
    }
  }

  async executeRecoveryTest() {
    console.log('🔄 Testando recuperação automática...');

    // Simular falha e medir tempo de recuperação
    const startTime = Date.now();

    try {
      // Fazer requisições normais
      await this.makeTestRequest();

      // Simular sobrecarga
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(this.makeTestRequest().catch(() => {}));
      }

      await Promise.allSettled(promises);

      // Aguardar recuperação
      await this.sleep(5000);

      // Testar se sistema se recuperou
      await this.makeTestRequest();

      const recoveryTime = Date.now() - startTime;

      return {
        recoveryTime,
        status: 'recovered'
      };

    } catch (error) {
      return {
        recoveryTime: Date.now() - startTime,
        status: 'failed',
        error: error.message
      };
    }
  }

  async executeMemoryTest() {
    console.log('🧠 Monitorando uso de memória...');

    const initialMemory = process.memoryUsage();
    const memorySnapshots = [initialMemory.heapUsed];

    // Fazer múltiplas requisições e monitorar memória
    for (let i = 0; i < 100; i++) {
      try {
        await this.makeTestRequest();
        memorySnapshots.push(process.memoryUsage().heapUsed);
      } catch (error) {
        // Continuar monitoramento mesmo com erros
      }

      if (i % 10 === 0) {
        // Forçar garbage collection se disponível
        if (global.gc) {
          global.gc();
        }
      }
    }

    const finalMemory = process.memoryUsage();
    const memoryGrowth = finalMemory.heapUsed - initialMemory.heapUsed;
    const avgMemoryPerRequest = memoryGrowth / 100;

    // Detectar possível vazamento (crescimento > 1MB)
    const leakDetected = memoryGrowth > 1024 * 1024;

    return {
      initialMemory: Math.round(initialMemory.heapUsed / 1024 / 1024),
      finalMemory: Math.round(finalMemory.heapUsed / 1024 / 1024),
      memoryGrowth: Math.round(memoryGrowth / 1024 / 1024),
      avgMemoryPerRequest: Math.round(avgMemoryPerRequest / 1024),
      leakDetected,
      snapshots: memorySnapshots.length
    };
  }

  async generateReport() {
    const report = {
      testSuite: 'Resilience Tests',
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.endTime - this.startTime,
      totalTests: this.results.length,
      passed: this.results.filter(r => r.status === 'SUCCESS').length,
      failed: this.results.filter(r => r.status === 'FAILED').length,
      critical: this.results.filter(r => r.status === 'CRITICAL').length,
      warnings: this.results.filter(r => r.status === 'WARNING').length,
      results: this.results
    };

    const reportFile = path.join(this.reportDir, `resilience-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

    console.log('📊 RELATÓRIO FINAL DE RESILIÊNCIA');
    console.log('=================================');
    console.log(`📁 Relatório salvo em: ${reportFile}`);
    console.log(`⏱️ Duração total: ${Math.round(report.duration / 1000)}s`);
    console.log(`✅ Testes aprovados: ${report.passed}`);
    console.log(`❌ Testes falharam: ${report.failed}`);
    console.log(`🚨 Falhas críticas: ${report.critical}`);
    console.log(`⚠️ Avisos: ${report.warnings}`);
    
    return report;
  }

  async generateErrorReport(error) {
    const errorReport = {
      error: error.message,
      stack: error.stack,
      timestamp: new Date(),
      partialResults: this.results
    };

    const errorFile = path.join(this.reportDir, `error-report-${Date.now()}.json`);
    fs.writeFileSync(errorFile, JSON.stringify(errorReport, null, 2));
    
    console.log(`💥 Relatório de erro salvo em: ${errorFile}`);
  }

  // Métodos auxiliares para testes de falha
  async simulateDatabaseFailure() {
    console.log('💥 Simulando desconexão do banco de dados...');

    // Fazer requisições que dependem do banco
    let successBefore = 0;
    let successAfter = 0;

    // Testar antes da "falha"
    for (let i = 0; i < 5; i++) {
      try {
        await this.makeTestRequest();
        successBefore++;
      } catch (error) {
        // Ignorar erros
      }
    }

    // Simular falha fazendo muitas requisições simultâneas
    const promises = [];
    for (let i = 0; i < 50; i++) {
      promises.push(this.makeTestRequest().catch(() => {}));
    }

    await Promise.allSettled(promises);

    // Aguardar "recuperação"
    await this.sleep(3000);

    // Testar após "recuperação"
    for (let i = 0; i < 5; i++) {
      try {
        await this.makeTestRequest();
        successAfter++;
      } catch (error) {
        // Ignorar erros
      }
    }

    return {
      type: 'Database Failure Simulation',
      successBefore,
      successAfter,
      recovered: successAfter > 0
    };
  }

  async simulateRedisFailure() {
    console.log('🔴 Simulando falha do Redis...');

    // Similar ao teste de banco, mas focado em cache
    let cacheHitsBefore = 0;
    let cacheHitsAfter = 0;

    // Fazer requisições que usam cache
    for (let i = 0; i < 10; i++) {
      try {
        await this.makeTestRequest();
        cacheHitsBefore++;
      } catch (error) {
        // Ignorar
      }
    }

    // Simular sobrecarga que pode afetar Redis
    const promises = [];
    for (let i = 0; i < 30; i++) {
      promises.push(this.makeTestRequest().catch(() => {}));
    }

    await Promise.allSettled(promises);
    await this.sleep(2000);

    // Testar recuperação
    for (let i = 0; i < 10; i++) {
      try {
        await this.makeTestRequest();
        cacheHitsAfter++;
      } catch (error) {
        // Ignorar
      }
    }

    return {
      type: 'Redis Failure Simulation',
      cacheHitsBefore,
      cacheHitsAfter,
      recovered: cacheHitsAfter > cacheHitsBefore * 0.5
    };
  }

  async simulateRequestFlood() {
    console.log('🌊 Simulando flood de requisições...');

    const floodSize = 200;
    const startTime = Date.now();

    // Criar flood de requisições
    const promises = [];
    for (let i = 0; i < floodSize; i++) {
      promises.push(this.makeTestRequest().catch(() => ({ error: true })));
    }

    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled' && !r.value.error).length;
    const failed = floodSize - successful;

    const duration = Date.now() - startTime;

    return {
      type: 'Request Flood',
      totalRequests: floodSize,
      successful,
      failed,
      duration,
      survivedFlood: successful > 0
    };
  }

  async simulateDataCorruption() {
    console.log('🗂️ Simulando corrupção de dados...');

    // Fazer requisições com dados inválidos
    let validRequests = 0;
    let invalidRequests = 0;

    const invalidData = [
      { email: null, password: 'test' },
      { email: 'invalid', password: null },
      { email: '', password: '' },
      { email: 'a'.repeat(1000), password: 'b'.repeat(1000) }
    ];

    for (const data of invalidData) {
      try {
        await this.makeTestRequest('/auth/login', data);
        validRequests++;
      } catch (error) {
        invalidRequests++;
      }
    }

    // Testar se sistema ainda responde a dados válidos
    try {
      await this.makeTestRequest();
      validRequests++;
    } catch (error) {
      invalidRequests++;
    }

    return {
      type: 'Data Corruption Simulation',
      validRequests,
      invalidRequests,
      systemStable: validRequests > 0
    };
  }

  async makeTestRequest(path = '/auth/login', data = null) {
    const http = require('http');

    return new Promise((resolve, reject) => {
      const postData = JSON.stringify(data || {
        email: '<EMAIL>',
        password: 'Test123!'
      });

      const options = {
        hostname: 'localhost',
        port: 3001,
        path,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData),
          'User-Agent': 'ResilienceTest'
        },
        timeout: 5000
      };

      const req = http.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => { responseData += chunk; });
        res.on('end', () => {
          if (res.statusCode < 500) {
            resolve({ status: res.statusCode, data: responseData });
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Timeout'));
      });

      req.write(postData);
      req.end();
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const suite = new ResilienceTestSuite();
  suite.runAllTests().catch(console.error);
}

module.exports = ResilienceTestSuite;
