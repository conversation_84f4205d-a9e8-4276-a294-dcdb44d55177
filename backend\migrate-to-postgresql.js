/**
 * Script de Migração para PostgreSQL
 * Migra dados do SQLite para PostgreSQL com validação completa
 */

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

class PostgreSQLMigrator {
  constructor() {
    this.sqlitePrisma = null;
    this.postgresqlPrisma = null;
    this.migrationReport = {
      startTime: new Date(),
      tables: {},
      errors: [],
      summary: {}
    };
  }

  /**
   * Executar migração completa
   */
  async migrate() {
    console.log('🔄 INICIANDO MIGRAÇÃO PARA POSTGRESQL');
    console.log('====================================');
    
    try {
      // 1. Configurar conexões
      await this.setupConnections();
      
      // 2. Validar esquemas
      await this.validateSchemas();
      
      // 3. Migrar dados
      await this.migrateData();
      
      // 4. Validar integridade
      await this.validateIntegrity();
      
      // 5. Gerar relatório
      this.generateReport();
      
      console.log('✅ MIGRAÇÃO CONCLUÍDA COM SUCESSO!');
      
    } catch (error) {
      console.error('❌ Erro na migração:', error.message);
      this.migrationReport.errors.push({
        type: 'CRITICAL',
        message: error.message,
        timestamp: new Date()
      });
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Configurar conexões com ambos os bancos
   */
  async setupConnections() {
    console.log('1️⃣ Configurando conexões...');
    
    // SQLite (origem)
    this.sqlitePrisma = new PrismaClient({
      datasources: {
        db: {
          url: "file:./prisma/dev.db"
        }
      }
    });
    
    // PostgreSQL (destino)
    const postgresUrl = process.env.POSTGRESQL_URL || 
      "postgresql://postgres:password@localhost:5432/code2post_prod";
    
    this.postgresqlPrisma = new PrismaClient({
      datasources: {
        db: {
          url: postgresUrl
        }
      }
    });
    
    // Testar conexões
    await this.sqlitePrisma.$connect();
    console.log('   ✅ SQLite conectado');
    
    await this.postgresqlPrisma.$connect();
    console.log('   ✅ PostgreSQL conectado');
  }

  /**
   * Validar esquemas entre bancos
   */
  async validateSchemas() {
    console.log('2️⃣ Validando esquemas...');
    
    // Verificar se PostgreSQL tem as tabelas necessárias
    const tables = ['User', 'Post', 'GitHubConfig'];
    
    for (const table of tables) {
      try {
        const tableName = table.toLowerCase();
        const result = await this.postgresqlPrisma.$queryRaw`
          SELECT table_name FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_name = ${tableName}
        `;
        
        if (result.length === 0) {
          throw new Error(`Tabela ${table} não encontrada no PostgreSQL`);
        }
        
        console.log(`   ✅ Tabela ${table} validada`);
      } catch (error) {
        console.error(`   ❌ Erro na tabela ${table}:`, error.message);
        throw error;
      }
    }
  }

  /**
   * Migrar dados das tabelas
   */
  async migrateData() {
    console.log('3️⃣ Migrando dados...');
    
    // Migrar usuários
    await this.migrateUsers();
    
    // Migrar posts
    await this.migratePosts();
    
    // Migrar configurações GitHub
    await this.migrateGitHubConfigs();
  }

  /**
   * Migrar tabela de usuários
   */
  async migrateUsers() {
    console.log('   👥 Migrando usuários...');
    
    try {
      const users = await this.sqlitePrisma.user.findMany();
      console.log(`      Encontrados ${users.length} usuários`);
      
      let migrated = 0;
      let errors = 0;
      
      for (const user of users) {
        try {
          await this.postgresqlPrisma.user.upsert({
            where: { id: user.id },
            update: user,
            create: user
          });
          migrated++;
        } catch (error) {
          console.error(`      ❌ Erro ao migrar usuário ${user.id}:`, error.message);
          errors++;
          this.migrationReport.errors.push({
            type: 'USER_MIGRATION',
            userId: user.id,
            message: error.message
          });
        }
      }
      
      this.migrationReport.tables.users = {
        total: users.length,
        migrated,
        errors
      };
      
      console.log(`      ✅ ${migrated} usuários migrados, ${errors} erros`);
      
    } catch (error) {
      console.error('   ❌ Erro na migração de usuários:', error.message);
      throw error;
    }
  }

  /**
   * Migrar tabela de posts
   */
  async migratePosts() {
    console.log('   📝 Migrando posts...');
    
    try {
      const posts = await this.sqlitePrisma.post.findMany();
      console.log(`      Encontrados ${posts.length} posts`);
      
      let migrated = 0;
      let errors = 0;
      
      for (const post of posts) {
        try {
          await this.postgresqlPrisma.post.upsert({
            where: { id: post.id },
            update: post,
            create: post
          });
          migrated++;
        } catch (error) {
          console.error(`      ❌ Erro ao migrar post ${post.id}:`, error.message);
          errors++;
          this.migrationReport.errors.push({
            type: 'POST_MIGRATION',
            postId: post.id,
            message: error.message
          });
        }
      }
      
      this.migrationReport.tables.posts = {
        total: posts.length,
        migrated,
        errors
      };
      
      console.log(`      ✅ ${migrated} posts migrados, ${errors} erros`);
      
    } catch (error) {
      console.error('   ❌ Erro na migração de posts:', error.message);
      throw error;
    }
  }

  /**
   * Migrar configurações GitHub
   */
  async migrateGitHubConfigs() {
    console.log('   🐙 Migrando configurações GitHub...');
    
    try {
      const configs = await this.sqlitePrisma.gitHubConfig.findMany();
      console.log(`      Encontradas ${configs.length} configurações`);
      
      let migrated = 0;
      let errors = 0;
      
      for (const config of configs) {
        try {
          await this.postgresqlPrisma.gitHubConfig.upsert({
            where: { id: config.id },
            update: config,
            create: config
          });
          migrated++;
        } catch (error) {
          console.error(`      ❌ Erro ao migrar config ${config.id}:`, error.message);
          errors++;
          this.migrationReport.errors.push({
            type: 'GITHUB_CONFIG_MIGRATION',
            configId: config.id,
            message: error.message
          });
        }
      }
      
      this.migrationReport.tables.githubConfigs = {
        total: configs.length,
        migrated,
        errors
      };
      
      console.log(`      ✅ ${migrated} configurações migradas, ${errors} erros`);
      
    } catch (error) {
      console.error('   ❌ Erro na migração de configurações GitHub:', error.message);
      throw error;
    }
  }

  /**
   * Validar integridade dos dados migrados
   */
  async validateIntegrity() {
    console.log('4️⃣ Validando integridade...');
    
    // Contar registros em ambos os bancos
    const sqliteCounts = {
      users: await this.sqlitePrisma.user.count(),
      posts: await this.sqlitePrisma.post.count(),
      githubConfigs: await this.sqlitePrisma.gitHubConfig.count()
    };
    
    const postgresqlCounts = {
      users: await this.postgresqlPrisma.user.count(),
      posts: await this.postgresqlPrisma.post.count(),
      githubConfigs: await this.postgresqlPrisma.gitHubConfig.count()
    };
    
    console.log('   📊 Comparação de registros:');
    console.log(`      Usuários: SQLite ${sqliteCounts.users} → PostgreSQL ${postgresqlCounts.users}`);
    console.log(`      Posts: SQLite ${sqliteCounts.posts} → PostgreSQL ${postgresqlCounts.posts}`);
    console.log(`      GitHub Configs: SQLite ${sqliteCounts.githubConfigs} → PostgreSQL ${postgresqlCounts.githubConfigs}`);
    
    // Validar integridade referencial
    await this.validateReferentialIntegrity();
    
    this.migrationReport.summary = {
      sqlite: sqliteCounts,
      postgresql: postgresqlCounts,
      integrity: 'VALIDATED'
    };
  }

  /**
   * Validar integridade referencial
   */
  async validateReferentialIntegrity() {
    console.log('   🔗 Validando integridade referencial...');
    
    // Verificar se todos os posts têm usuários válidos
    const orphanPosts = await this.postgresqlPrisma.post.findMany({
      where: {
        user: null
      }
    });
    
    if (orphanPosts.length > 0) {
      console.warn(`   ⚠️ ${orphanPosts.length} posts órfãos encontrados`);
      this.migrationReport.errors.push({
        type: 'REFERENTIAL_INTEGRITY',
        message: `${orphanPosts.length} posts sem usuário válido`
      });
    } else {
      console.log('   ✅ Integridade referencial validada');
    }
  }

  /**
   * Gerar relatório da migração
   */
  generateReport() {
    console.log('5️⃣ Gerando relatório...');
    
    this.migrationReport.endTime = new Date();
    this.migrationReport.duration = this.migrationReport.endTime - this.migrationReport.startTime;
    
    const reportPath = `migration-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(this.migrationReport, null, 2));
    
    console.log(`   📄 Relatório salvo: ${reportPath}`);
    
    // Resumo no console
    console.log('\n📊 RESUMO DA MIGRAÇÃO:');
    console.log(`   ⏱️ Duração: ${Math.round(this.migrationReport.duration / 1000)}s`);
    console.log(`   👥 Usuários: ${this.migrationReport.tables.users?.migrated || 0} migrados`);
    console.log(`   📝 Posts: ${this.migrationReport.tables.posts?.migrated || 0} migrados`);
    console.log(`   🐙 GitHub Configs: ${this.migrationReport.tables.githubConfigs?.migrated || 0} migrados`);
    console.log(`   ❌ Erros: ${this.migrationReport.errors.length}`);
  }

  /**
   * Limpeza das conexões
   */
  async cleanup() {
    if (this.sqlitePrisma) {
      await this.sqlitePrisma.$disconnect();
    }
    if (this.postgresqlPrisma) {
      await this.postgresqlPrisma.$disconnect();
    }
  }
}

// Executar migração se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const migrator = new PostgreSQLMigrator();
  migrator.migrate().catch(console.error);
}

export { PostgreSQLMigrator };
