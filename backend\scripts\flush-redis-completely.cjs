/**
 * Script para limpar COMPLETAMENTE o Redis
 */

async function flushRedisCompletely() {
  console.log('🧹 LIMPANDO REDIS COMPLETAMENTE...');
  
  try {
    const redisService = require('../src/services/redisService.js').default;
    
    // Conectar ao Redis
    const client = await redisService.getClient();
    
    if (!client) {
      console.log('❌ Redis não está disponível');
      return;
    }
    
    // FLUSH ALL - remove TODAS as chaves
    await client.flushAll();
    console.log('✅ REDIS COMPLETAMENTE LIMPO!');
    
    // Verificar se está vazio
    const keys = await client.keys('*');
    console.log(`📊 Chaves restantes: ${keys.length}`);
    
    if (keys.length === 0) {
      console.log('🎉 Redis está completamente vazio!');
    } else {
      console.log('⚠️ Ainda há chaves no Redis:', keys);
    }
    
  } catch (error) {
    console.error('❌ Erro ao limpar Redis:', error.message);
  }
}

flushRedisCompletely();
