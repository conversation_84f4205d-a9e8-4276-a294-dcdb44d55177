/**
 * Setup global para testes Jest
 */

import { jest } from '@jest/globals';

// Configurar timeout global
jest.setTimeout(30000);

// Mock do console para testes mais limpos
global.console = {
  ...console,
  // Manter apenas logs importantes durante testes
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: console.warn,
  error: console.error,
};

// Mock de variáveis de ambiente para testes
process.env.NODE_ENV = 'test';
process.env.TESTING = 'true';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.CSRF_SECRET = 'test-csrf-secret-key-for-testing-only';
process.env.DATABASE_URL = 'file:./prisma/test.db';
process.env.REDIS_URL = 'redis://localhost:6379/15'; // DB 15 para testes

// Configurações globais de teste
global.testConfig = {
  timeout: 10000,
  retries: 3,
  baseUrl: 'http://localhost:3001'
};

// Utilitários globais para testes
global.testUtils = {
  /**
   * Aguardar um tempo específico
   */
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  /**
   * Gerar dados de teste aleatórios
   */
  generateTestData: {
    user: () => ({
      email: `test-${Date.now()}@example.com`,
      name: `Test User ${Date.now()}`,
      password: 'TestPassword123!'
    }),
    
    post: (userId) => ({
      title: `Test Post ${Date.now()}`,
      content: 'This is a test post content',
      status: 'DRAFT',
      platform: 'LINKEDIN',
      userId
    })
  },
  
  /**
   * Limpar dados de teste
   */
  cleanup: async (prisma) => {
    if (prisma) {
      await prisma.post.deleteMany({
        where: {
          title: {
            startsWith: 'Test Post'
          }
        }
      });
      
      await prisma.user.deleteMany({
        where: {
          email: {
            startsWith: 'test-'
          }
        }
      });
    }
  }
};

// Setup antes de todos os testes
beforeAll(async () => {
  // Configurações globais antes dos testes
  console.log('🧪 Iniciando setup de testes...');
});

// Cleanup após todos os testes
afterAll(async () => {
  // Limpeza global após os testes
  console.log('🧹 Finalizando testes...');
});

// Setup antes de cada teste
beforeEach(() => {
  // Reset de mocks antes de cada teste
  jest.clearAllMocks();
});

// Cleanup após cada teste
afterEach(() => {
  // Limpeza após cada teste se necessário
});
