/**
 * Teste simples de login para verificar rate limiting
 */

const http = require('http');

async function testLogin() {
  console.log('🧪 Testando login simples...');
  
  try {
    const result = await makeRequest('/auth/login', 'POST', {
      email: '<EMAIL>',
      password: 'LoadTest123!'
    });
    
    console.log(`Status: ${result.status}`);
    console.log(`Headers:`, result.headers);
    console.log(`Data:`, result.data);
    
  } catch (error) {
    console.error('Erro:', error.message);
  }
}

async function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : '';
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestScript'
      },
      timeout: 10000
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => { responseData += chunk; });
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(postData);
    }
    
    req.end();
  });
}

testLogin().catch(console.error);
