/**
 * Teste do Sistema de Cache de Resposta
 */

const http = require('http');

class ResponseCacheTest {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.results = [];
  }

  async runTests() {
    console.log('🧪 TESTANDO SISTEMA DE CACHE DE RESPOSTA');
    console.log('========================================');

    try {
      // Teste 1: Cache Miss (primeira requisição)
      console.log('\n1️⃣ Testando Cache Miss...');
      const miss1 = await this.makeRequest('/health');
      console.log(`   Status: ${miss1.status}`);
      console.log(`   Cache: ${miss1.headers['x-cache'] || 'N/A'}`);
      console.log(`   Tempo: ${miss1.responseTime}ms`);

      // Teste 2: Cache Hit (segunda requisição)
      console.log('\n2️⃣ Testando Cache Hit...');
      const hit1 = await this.makeRequest('/health');
      console.log(`   Status: ${hit1.status}`);
      console.log(`   Cache: ${hit1.headers['x-cache'] || 'N/A'}`);
      console.log(`   Tempo: ${hit1.responseTime}ms`);

      // Teste 3: Comparação de performance
      console.log('\n3️⃣ Comparando Performance...');
      const improvement = miss1.responseTime > hit1.responseTime ? 
        ((miss1.responseTime - hit1.responseTime) / miss1.responseTime * 100).toFixed(1) : 0;
      console.log(`   Melhoria: ${improvement}% mais rápido com cache`);

      // Teste 4: Múltiplas requisições para verificar consistência
      console.log('\n4️⃣ Testando Consistência do Cache...');
      const requests = [];
      for (let i = 0; i < 5; i++) {
        requests.push(this.makeRequest('/health'));
      }
      
      const responses = await Promise.all(requests);
      const cacheHits = responses.filter(r => r.headers['x-cache'] === 'HIT').length;
      const cacheMisses = responses.filter(r => r.headers['x-cache'] === 'MISS').length;
      
      console.log(`   Cache Hits: ${cacheHits}/5`);
      console.log(`   Cache Misses: ${cacheMisses}/5`);
      console.log(`   Taxa de Hit: ${(cacheHits/5*100).toFixed(1)}%`);

      // Teste 5: Diferentes endpoints
      console.log('\n5️⃣ Testando Diferentes Endpoints...');
      const endpoints = ['/health', '/csrf-token'];
      
      for (const endpoint of endpoints) {
        const response = await this.makeRequest(endpoint);
        console.log(`   ${endpoint}: ${response.status} - Cache: ${response.headers['x-cache'] || 'N/A'}`);
      }

      // Teste 6: Verificar headers de cache
      console.log('\n6️⃣ Verificando Headers de Cache...');
      const cacheResponse = await this.makeRequest('/health');
      const cacheHeaders = Object.keys(cacheResponse.headers)
        .filter(h => h.toLowerCase().includes('cache') || h.toLowerCase().includes('x-'))
        .reduce((obj, key) => {
          obj[key] = cacheResponse.headers[key];
          return obj;
        }, {});
      
      console.log('   Headers de Cache:');
      Object.entries(cacheHeaders).forEach(([key, value]) => {
        console.log(`      ${key}: ${value}`);
      });

      console.log('\n✅ TODOS OS TESTES DE CACHE CONCLUÍDOS!');
      console.log('=======================================');

    } catch (error) {
      console.error('❌ Erro nos testes de cache:', error.message);
    }
  }

  async makeRequest(path) {
    const startTime = Date.now();
    
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3001,
        path,
        method: 'GET',
        timeout: 10000
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          const responseTime = Date.now() - startTime;
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data,
            responseTime
          });
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }
}

// Executar testes
const tester = new ResponseCacheTest();
tester.runTests().catch(console.error);
