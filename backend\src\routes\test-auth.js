/**
 * Rotas de autenticação APENAS PARA TESTES DE RESILIÊNCIA
 * SEM rate limiting ou middlewares que possam interferir
 */

import express from 'express';
import userServicePrisma from '../services/userServicePrisma.js';

const router = express.Router();

/**
 * Rota de login APENAS para testes de resiliência
 * SEM rate limiting, CSRF, ou outros middlewares
 */
router.post('/login', async (req, res) => {
  try {
    console.log('🧪 ROTA DE TESTE - Login para testes de resiliência');
    console.log('🧪 Body:', req.body);
    
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ 
        error: 'Email e senha são obrigatórios',
        test: true 
      });
    }
    
    console.log('🧪 Tentativa de login de teste:', { email, password: '***' });

    // Verificar credenciais usando o serviço
    const result = await userServicePrisma.verifyCredentials(email, password);
    
    console.log('🧪 Resultado da verificação de teste:', result);

    if (!result.success) {
      return res.status(401).json({ 
        error: result.error,
        test: true 
      });
    }

    const user = result.user;

    // Retornar sucesso SEM criar token JWT (apenas para teste)
    res.json({
      success: true,
      message: 'Login de teste bem-sucedido',
      user: {
        id: user.id,
        email: user.email,
        name: user.name
      },
      test: true,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro no login de teste:', error.message);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      details: error.message,
      test: true 
    });
  }
});

/**
 * Rota de health check para testes
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Rota de teste funcionando',
    test: true,
    timestamp: new Date().toISOString()
  });
});

/**
 * Rota para simular diferentes cenários de teste
 */
router.post('/simulate/:scenario', async (req, res) => {
  const { scenario } = req.params;
  
  console.log(`🧪 Simulando cenário: ${scenario}`);
  
  switch (scenario) {
    case 'slow':
      // Simular resposta lenta
      await new Promise(resolve => setTimeout(resolve, 2000));
      res.json({ scenario: 'slow', delay: '2000ms', test: true });
      break;
      
    case 'error':
      // Simular erro interno
      res.status(500).json({ 
        scenario: 'error', 
        error: 'Erro simulado para teste',
        test: true 
      });
      break;
      
    case 'timeout':
      // Simular timeout (não responder)
      // Não enviar resposta
      break;
      
    case 'memory':
      // Simular uso de memória
      const bigArray = new Array(1000000).fill('test');
      res.json({ 
        scenario: 'memory', 
        allocated: bigArray.length,
        test: true 
      });
      break;
      
    default:
      res.json({ 
        scenario: 'default', 
        message: 'Cenário não reconhecido',
        test: true 
      });
  }
});

export default router;
