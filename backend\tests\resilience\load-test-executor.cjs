/**
 * Executor de Testes de Carga
 * Simula múltiplos usuários simultâneos fazendo login
 */

const http = require('http');
const https = require('https');

class LoadTestExecutor {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.results = [];
    this.activeRequests = 0;
    this.totalRequests = 0;
    this.successfulRequests = 0;
    this.failedRequests = 0;
    this.responseTimes = [];
  }

  async execute(testConfig) {
    console.log(`🚀 Iniciando teste de carga: ${testConfig.name}`);
    console.log(`👥 Usuários simultâneos: ${testConfig.users}`);
    console.log(`⏱️ Duração: ${testConfig.duration}s`);
    
    const startTime = Date.now();
    const endTime = startTime + (testConfig.duration * 1000);
    
    // Reset counters
    this.resetCounters();
    
    // Criar usuários virtuais
    const users = this.createVirtualUsers(testConfig.users);
    
    // Iniciar teste
    const userPromises = users.map(user => this.simulateUser(user, endTime));
    
    // Aguardar conclusão
    await Promise.allSettled(userPromises);
    
    const actualDuration = Date.now() - startTime;
    
    return this.calculateMetrics(actualDuration);
  }

  createVirtualUsers(count) {
    const users = [];
    for (let i = 0; i < count; i++) {
      users.push({
        id: i + 1,
        email: `loadtest${i + 1}@code2post.com`,
        password: 'LoadTest123!',
        requestInterval: 1000 + Math.random() * 2000 // 1-3 segundos entre requests
      });
    }
    return users;
  }

  async simulateUser(user, endTime) {
    while (Date.now() < endTime) {
      try {
        await this.makeLoginRequest(user);
        
        // Aguardar antes da próxima requisição
        await this.sleep(user.requestInterval);
        
      } catch (error) {
        // Usuário continua tentando mesmo com erros
        await this.sleep(user.requestInterval);
      }
    }
  }

  async makeLoginRequest(user) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      this.activeRequests++;
      this.totalRequests++;
      
      const postData = JSON.stringify({
        email: user.email,
        password: user.password
      });
      
      const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/auth/login',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData),
          'User-Agent': `LoadTest-User-${user.id}`
        },
        timeout: 10000 // 10 segundos timeout
      };
      
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          const responseTime = Date.now() - startTime;
          this.responseTimes.push(responseTime);
          this.activeRequests--;
          
          if (res.statusCode === 200 || res.statusCode === 401) {
            // 401 é esperado para usuários que não existem
            this.successfulRequests++;
            resolve({
              status: res.statusCode,
              responseTime,
              data: data.substring(0, 100) // Primeiros 100 chars
            });
          } else {
            this.failedRequests++;
            reject(new Error(`HTTP ${res.statusCode}: ${data.substring(0, 100)}`));
          }
        });
      });
      
      req.on('error', (error) => {
        this.activeRequests--;
        this.failedRequests++;
        reject(error);
      });
      
      req.on('timeout', () => {
        req.destroy();
        this.activeRequests--;
        this.failedRequests++;
        reject(new Error('Request timeout'));
      });
      
      req.write(postData);
      req.end();
    });
  }

  calculateMetrics(duration) {
    const durationSeconds = duration / 1000;
    const avgRPS = Math.round(this.totalRequests / durationSeconds * 100) / 100;
    const errorRate = Math.round((this.failedRequests / this.totalRequests) * 100 * 100) / 100;

    // Calcular latências (com proteção contra array vazio)
    let avgLatency = 0;
    let p50 = 0;
    let p95 = 0;
    let p99 = 0;
    let maxLatency = 0;
    let minLatency = 0;

    if (this.responseTimes.length > 0) {
      this.responseTimes.sort((a, b) => a - b);
      avgLatency = Math.round(this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length);
      p50 = this.responseTimes[Math.floor(this.responseTimes.length * 0.5)] || 0;
      p95 = this.responseTimes[Math.floor(this.responseTimes.length * 0.95)] || 0;
      p99 = this.responseTimes[Math.floor(this.responseTimes.length * 0.99)] || 0;
      maxLatency = this.responseTimes[this.responseTimes.length - 1] || 0;
      minLatency = this.responseTimes[0] || 0;
    }
    
    return {
      duration: durationSeconds,
      totalRequests: this.totalRequests,
      successfulRequests: this.successfulRequests,
      failedRequests: this.failedRequests,
      avgRPS,
      errorRate,
      avgLatency,
      minLatency,
      maxLatency,
      p50,
      p95,
      p99,
      throughput: avgRPS,
      concurrency: this.activeRequests
    };
  }

  resetCounters() {
    this.activeRequests = 0;
    this.totalRequests = 0;
    this.successfulRequests = 0;
    this.failedRequests = 0;
    this.responseTimes = [];
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Teste individual se executado diretamente
async function runStandaloneTest() {
  const executor = new LoadTestExecutor();
  
  const testConfig = {
    name: 'Teste Standalone',
    users: 10,
    duration: 30
  };
  
  try {
    console.log('🧪 Executando teste de carga standalone...');
    const result = await executor.execute(testConfig);
    
    console.log('📊 Resultados:');
    console.log(`   RPS médio: ${result.avgRPS}`);
    console.log(`   Latência média: ${result.avgLatency}ms`);
    console.log(`   Taxa de erro: ${result.errorRate}%`);
    console.log(`   P95: ${result.p95}ms`);
    console.log(`   P99: ${result.p99}ms`);
    console.log(`   Total de requests: ${result.totalRequests}`);
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }
}

if (require.main === module) {
  runStandaloneTest();
}

module.exports = {
  execute: async (testConfig) => {
    const executor = new LoadTestExecutor();
    return await executor.execute(testConfig);
  }
};
