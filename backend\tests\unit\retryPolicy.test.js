/**
 * Testes de Unidade para Retry Policy
 */

import { jest, describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { 
  RetryPolicy, 
  BackoffStrategies, 
  calculateDelay, 
  isRetryableError 
} from '../../src/utils/retryPolicy.js';

describe('RetryPolicy', () => {
  let retryPolicy;
  let mockFunction;

  beforeEach(() => {
    retryPolicy = new RetryPolicy({
      name: 'test-retry',
      maxAttempts: 3,
      baseDelay: 100,
      strategy: BackoffStrategies.FIXED,
      retryableErrors: ['RETRYABLE_ERROR']
    });
    
    mockFunction = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Execução com Sucesso', () => {
    test('deve executar função com sucesso na primeira tentativa', async () => {
      mockFunction.mockResolvedValue('success');
      
      const result = await retryPolicy.execute(mockFunction, 'arg1', 'arg2');
      
      expect(result).toBe('success');
      expect(mockFunction).toHaveBeenCalledTimes(1);
      expect(mockFunction).toHaveBeenCalledWith('arg1', 'arg2');
      expect(retryPolicy.stats.successfulAttempts).toBe(1);
      expect(retryPolicy.stats.totalAttempts).toBe(1);
    });

    test('deve executar função com sucesso após falhas', async () => {
      mockFunction.mockRejectedValueOnce(new Error('RETRYABLE_ERROR'));
      mockFunction.mockRejectedValueOnce(new Error('RETRYABLE_ERROR'));
      mockFunction.mockResolvedValue('success');
      
      const result = await retryPolicy.execute(mockFunction);
      
      expect(result).toBe('success');
      expect(mockFunction).toHaveBeenCalledTimes(3);
      expect(retryPolicy.stats.successfulAttempts).toBe(1);
      expect(retryPolicy.stats.retriedAttempts).toBe(2);
    });
  });

  describe('Execução com Falha', () => {
    test('deve falhar após esgotar tentativas', async () => {
      mockFunction.mockRejectedValue(new Error('RETRYABLE_ERROR'));
      
      await expect(retryPolicy.execute(mockFunction))
        .rejects.toThrow('RETRYABLE_ERROR');
      
      expect(mockFunction).toHaveBeenCalledTimes(3);
      expect(retryPolicy.stats.failedAttempts).toBe(1);
      expect(retryPolicy.stats.retriedAttempts).toBe(2);
    });

    test('deve falhar imediatamente para erros não retryable', async () => {
      mockFunction.mockRejectedValue(new Error('NON_RETRYABLE_ERROR'));
      
      await expect(retryPolicy.execute(mockFunction))
        .rejects.toThrow('NON_RETRYABLE_ERROR');
      
      expect(mockFunction).toHaveBeenCalledTimes(1);
      expect(retryPolicy.stats.failedAttempts).toBe(1);
      expect(retryPolicy.stats.retriedAttempts).toBe(0);
    });
  });

  describe('Estratégias de Backoff', () => {
    test('FIXED deve usar delay fixo', () => {
      const delay1 = calculateDelay(1, 100, BackoffStrategies.FIXED);
      const delay2 = calculateDelay(2, 100, BackoffStrategies.FIXED);
      const delay3 = calculateDelay(3, 100, BackoffStrategies.FIXED);
      
      expect(delay1).toBe(100);
      expect(delay2).toBe(100);
      expect(delay3).toBe(100);
    });

    test('LINEAR deve usar delay linear', () => {
      const delay1 = calculateDelay(1, 100, BackoffStrategies.LINEAR);
      const delay2 = calculateDelay(2, 100, BackoffStrategies.LINEAR);
      const delay3 = calculateDelay(3, 100, BackoffStrategies.LINEAR);
      
      expect(delay1).toBe(100);
      expect(delay2).toBe(200);
      expect(delay3).toBe(300);
    });

    test('EXPONENTIAL deve usar delay exponencial', () => {
      const delay1 = calculateDelay(1, 100, BackoffStrategies.EXPONENTIAL);
      const delay2 = calculateDelay(2, 100, BackoffStrategies.EXPONENTIAL);
      const delay3 = calculateDelay(3, 100, BackoffStrategies.EXPONENTIAL);
      
      expect(delay1).toBe(100);
      expect(delay2).toBe(200);
      expect(delay3).toBe(400);
    });

    test('EXPONENTIAL_JITTER deve adicionar jitter', () => {
      const delays = [];
      for (let i = 0; i < 10; i++) {
        delays.push(calculateDelay(2, 100, BackoffStrategies.EXPONENTIAL_JITTER));
      }
      
      // Deve haver variação nos delays (jitter)
      const uniqueDelays = new Set(delays);
      expect(uniqueDelays.size).toBeGreaterThan(1);
      
      // Todos os delays devem estar próximos do valor base exponencial
      delays.forEach(delay => {
        expect(delay).toBeGreaterThan(180); // 200 - 10%
        expect(delay).toBeLessThan(220); // 200 + 10%
      });
    });

    test('deve respeitar maxDelay', () => {
      const delay = calculateDelay(10, 100, BackoffStrategies.EXPONENTIAL, 500);
      expect(delay).toBeLessThanOrEqual(500);
    });
  });

  describe('Detecção de Erros Retryable', () => {
    test('deve identificar erros de rede como retryable', () => {
      const networkErrors = [
        { code: 'ECONNREFUSED' },
        { code: 'ETIMEDOUT' },
        { code: 'ENOTFOUND' },
        { code: 'ECONNRESET' }
      ];
      
      networkErrors.forEach(error => {
        expect(isRetryableError(error)).toBe(true);
      });
    });

    test('deve identificar erros HTTP retryable', () => {
      const httpErrors = [
        { status: 408 }, // Request Timeout
        { status: 429 }, // Too Many Requests
        { status: 500 }, // Internal Server Error
        { status: 502 }, // Bad Gateway
        { status: 503 }, // Service Unavailable
        { status: 504 }  // Gateway Timeout
      ];
      
      httpErrors.forEach(error => {
        expect(isRetryableError(error)).toBe(true);
      });
    });

    test('deve identificar erros do Prisma como retryable', () => {
      const prismaErrors = [
        { code: 'P1001' }, // Can't reach database server
        { code: 'P1008' }, // Operations timed out
        { code: 'P1017' }  // Server has closed the connection
      ];
      
      prismaErrors.forEach(error => {
        expect(isRetryableError(error)).toBe(true);
      });
    });

    test('deve identificar erros customizados como retryable', () => {
      const customErrors = ['CUSTOM_RETRYABLE', /timeout/i, (err) => err.temporary];
      
      expect(isRetryableError({ message: 'CUSTOM_RETRYABLE error' }, customErrors)).toBe(true);
      expect(isRetryableError({ message: 'Connection timeout' }, customErrors)).toBe(true);
      expect(isRetryableError({ temporary: true }, customErrors)).toBe(true);
      expect(isRetryableError({ message: 'Permanent error' }, customErrors)).toBe(false);
    });
  });

  describe('Estatísticas', () => {
    test('deve coletar estatísticas corretamente', async () => {
      mockFunction.mockResolvedValueOnce('success1');
      mockFunction.mockRejectedValueOnce(new Error('RETRYABLE_ERROR'));
      mockFunction.mockResolvedValueOnce('success2');
      mockFunction.mockRejectedValue(new Error('NON_RETRYABLE_ERROR'));
      
      // Sucesso imediato
      await retryPolicy.execute(mockFunction);
      
      // Sucesso após retry
      await retryPolicy.execute(mockFunction);
      
      // Falha não retryable
      try {
        await retryPolicy.execute(mockFunction);
      } catch (error) {}
      
      const stats = retryPolicy.getStats();
      
      expect(stats.name).toBe('test-retry');
      expect(stats.totalAttempts).toBe(4); // 1 + 2 + 1
      expect(stats.successfulAttempts).toBe(2);
      expect(stats.failedAttempts).toBe(1);
      expect(stats.retriedAttempts).toBe(1);
      expect(stats.successRate).toBe('66.67%');
    });

    test('deve calcular taxa de retry corretamente', async () => {
      mockFunction.mockRejectedValueOnce(new Error('RETRYABLE_ERROR'));
      mockFunction.mockResolvedValue('success');
      
      await retryPolicy.execute(mockFunction);
      
      const stats = retryPolicy.getStats();
      expect(stats.retryRate).toBe('50.00%'); // 1 retry de 2 tentativas totais
    });

    test('deve resetar estatísticas', () => {
      retryPolicy.stats.totalAttempts = 10;
      retryPolicy.stats.successfulAttempts = 8;
      
      retryPolicy.resetStats();
      
      expect(retryPolicy.stats.totalAttempts).toBe(0);
      expect(retryPolicy.stats.successfulAttempts).toBe(0);
    });
  });

  describe('Callbacks', () => {
    test('deve chamar callback onRetry', async () => {
      const onRetry = jest.fn();
      const retryWithCallback = new RetryPolicy({
        maxAttempts: 3,
        baseDelay: 10,
        onRetry
      });
      
      mockFunction.mockRejectedValueOnce(new Error('ECONNREFUSED'));
      mockFunction.mockResolvedValue('success');
      
      await retryWithCallback.execute(mockFunction);
      
      expect(onRetry).toHaveBeenCalledTimes(1);
      expect(onRetry).toHaveBeenCalledWith(
        expect.any(Error),
        1, // attempt number
        expect.any(Number) // delay
      );
    });

    test('deve chamar callback onFailure', async () => {
      const onFailure = jest.fn();
      const retryWithCallback = new RetryPolicy({
        maxAttempts: 2,
        onFailure
      });
      
      mockFunction.mockRejectedValue(new Error('ECONNREFUSED'));
      
      try {
        await retryWithCallback.execute(mockFunction);
      } catch (error) {}
      
      expect(onFailure).toHaveBeenCalledTimes(1);
      expect(onFailure).toHaveBeenCalledWith(
        expect.any(Error),
        2 // final attempt number
      );
    });
  });

  describe('Configurações Padrão', () => {
    test('deve usar configurações padrão', () => {
      const defaultPolicy = new RetryPolicy();
      
      expect(defaultPolicy.maxAttempts).toBe(3);
      expect(defaultPolicy.baseDelay).toBe(1000);
      expect(defaultPolicy.strategy).toBe(BackoffStrategies.EXPONENTIAL_JITTER);
      expect(defaultPolicy.name).toBe('default');
    });
  });
});
