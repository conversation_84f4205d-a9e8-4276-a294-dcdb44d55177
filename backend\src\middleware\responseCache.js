/**
 * Middleware de Cache de Resposta para <PERSON><PERSON><PERSON><PERSON>
 * Implementa cache inteligente para endpoints de leitura frequente
 */

import redisService from '../services/redisService.js';
import crypto from 'crypto';

class ResponseCacheMiddleware {
  constructor() {
    this.prefix = 'response_cache:';
    this.defaultTTL = 300; // 5 minutos
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0,
      totalRequests: 0
    };
    
    // Configurações de cache por endpoint
    this.endpointConfig = {
      '/health': { ttl: 60, enabled: true },
      '/api/github/repositories': { ttl: 600, enabled: true }, // 10 minutos
      '/api/posts': { ttl: 300, enabled: true }, // 5 minutos
      '/api/posts/:id': { ttl: 600, enabled: true }, // 10 minutos
      '/csrf-token': { ttl: 3600, enabled: true }, // 1 hora
      '/api/user/profile': { ttl: 300, enabled: true } // 5 minutos
    };
    
    console.log('🚀 Response Cache Middleware inicializado');
  }

  /**
   * Criar middleware de cache
   */
  create(options = {}) {
    const {
      ttl = this.defaultTTL,
      enabled = true,
      keyGenerator = this.defaultKeyGenerator,
      shouldCache = this.defaultShouldCache,
      onHit = () => {},
      onMiss = () => {},
      onError = () => {}
    } = options;

    return async (req, res, next) => {
      this.stats.totalRequests++;

      // Verificar se cache está habilitado
      if (!enabled || !this.isCacheable(req)) {
        return next();
      }

      try {
        const cacheKey = keyGenerator(req);
        const cached = await this.getFromCache(cacheKey);

        if (cached) {
          // Cache hit
          this.stats.hits++;
          onHit(req, cached);
          
          // Adicionar headers de cache
          res.set({
            'X-Cache': 'HIT',
            'X-Cache-Key': cacheKey,
            'X-Cache-TTL': cached.ttl,
            'Content-Type': cached.contentType || 'application/json'
          });

          return res.status(cached.statusCode || 200).send(cached.data);
        }

        // Cache miss - interceptar resposta
        this.stats.misses++;
        onMiss(req);

        const originalSend = res.send;
        const originalJson = res.json;

        res.send = (data) => {
          this.cacheResponse(cacheKey, {
            statusCode: res.statusCode,
            data,
            contentType: res.get('Content-Type'),
            ttl
          });

          res.set({
            'X-Cache': 'MISS',
            'X-Cache-Key': cacheKey
          });

          return originalSend.call(res, data);
        };

        res.json = (data) => {
          this.cacheResponse(cacheKey, {
            statusCode: res.statusCode,
            data: JSON.stringify(data),
            contentType: 'application/json',
            ttl
          });

          res.set({
            'X-Cache': 'MISS',
            'X-Cache-Key': cacheKey
          });

          return originalJson.call(res, data);
        };

        next();

      } catch (error) {
        this.stats.errors++;
        onError(error);
        console.error('❌ Erro no cache de resposta:', error.message);
        next(); // Continuar sem cache em caso de erro
      }
    };
  }

  /**
   * Verificar se request é cacheável
   */
  isCacheable(req) {
    // Apenas métodos GET são cacheáveis
    if (req.method !== 'GET') {
      return false;
    }

    // Verificar configuração do endpoint
    const config = this.getEndpointConfig(req.path);
    return config?.enabled || false;
  }

  /**
   * Obter configuração do endpoint
   */
  getEndpointConfig(path) {
    // Busca exata primeiro
    if (this.endpointConfig[path]) {
      return this.endpointConfig[path];
    }

    // Busca por padrão (ex: /api/posts/:id)
    for (const [pattern, config] of Object.entries(this.endpointConfig)) {
      if (this.matchesPattern(path, pattern)) {
        return config;
      }
    }

    return null;
  }

  /**
   * Verificar se path corresponde ao padrão
   */
  matchesPattern(path, pattern) {
    const regex = pattern.replace(/:[\w]+/g, '[^/]+');
    return new RegExp(`^${regex}$`).test(path);
  }

  /**
   * Gerador de chave padrão
   */
  defaultKeyGenerator(req) {
    const baseKey = `${req.method}:${req.path}`;
    
    // Incluir query parameters relevantes
    const relevantParams = ['page', 'limit', 'sort', 'filter'];
    const queryParams = relevantParams
      .filter(param => req.query[param])
      .map(param => `${param}=${req.query[param]}`)
      .join('&');

    // Incluir user ID se autenticado (para cache por usuário)
    const userId = req.user?.id || 'anonymous';

    const fullKey = `${baseKey}?${queryParams}&user=${userId}`;
    
    // Hash para chave mais curta
    return crypto.createHash('md5').update(fullKey).digest('hex');
  }

  /**
   * Verificar se deve cachear resposta
   */
  defaultShouldCache(req, res) {
    // Não cachear erros
    if (res.statusCode >= 400) {
      return false;
    }

    // Não cachear respostas muito grandes (>1MB)
    const contentLength = res.get('Content-Length');
    if (contentLength && parseInt(contentLength) > 1024 * 1024) {
      return false;
    }

    return true;
  }

  /**
   * Obter resposta do cache
   */
  async getFromCache(key) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return null;
      }

      const cached = await client.get(`${this.prefix}${key}`);
      return cached ? JSON.parse(cached) : null;

    } catch (error) {
      console.error('❌ Erro ao ler cache:', error.message);
      return null;
    }
  }

  /**
   * Armazenar resposta no cache
   */
  async cacheResponse(key, response) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return false;
      }

      const cacheData = {
        ...response,
        cachedAt: new Date().toISOString()
      };

      await client.setEx(
        `${this.prefix}${key}`,
        response.ttl,
        JSON.stringify(cacheData)
      );

      return true;

    } catch (error) {
      console.error('❌ Erro ao armazenar cache:', error.message);
      return false;
    }
  }

  /**
   * Invalidar cache por padrão
   */
  async invalidatePattern(pattern) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return 0;
      }

      const keys = await client.keys(`${this.prefix}*${pattern}*`);
      if (keys.length === 0) {
        return 0;
      }

      const deleted = await client.del(...keys);
      console.log(`🗑️ ${deleted} chaves de cache invalidadas para padrão: ${pattern}`);
      
      return deleted;

    } catch (error) {
      console.error('❌ Erro ao invalidar cache:', error.message);
      return 0;
    }
  }

  /**
   * Invalidar cache específico
   */
  async invalidateKey(key) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return false;
      }

      const deleted = await client.del(`${this.prefix}${key}`);
      return deleted > 0;

    } catch (error) {
      console.error('❌ Erro ao invalidar chave:', error.message);
      return false;
    }
  }

  /**
   * Obter estatísticas do cache
   */
  getStats() {
    const hitRate = this.stats.totalRequests > 0 ? 
      (this.stats.hits / this.stats.totalRequests * 100).toFixed(2) : '0.00';

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      missRate: `${(100 - parseFloat(hitRate)).toFixed(2)}%`
    };
  }

  /**
   * Resetar estatísticas
   */
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0,
      totalRequests: 0
    };
  }

  /**
   * Middleware específico para diferentes endpoints
   */
  health() {
    return this.create({
      ttl: 60,
      enabled: true
    });
  }

  repositories() {
    return this.create({
      ttl: 600, // 10 minutos
      enabled: true,
      keyGenerator: (req) => {
        const userId = req.user?.id || 'anonymous';
        return crypto.createHash('md5')
          .update(`repositories:${userId}:${JSON.stringify(req.query)}`)
          .digest('hex');
      }
    });
  }

  posts() {
    return this.create({
      ttl: 300, // 5 minutos
      enabled: true,
      keyGenerator: (req) => {
        const userId = req.user?.id || 'anonymous';
        const params = { ...req.query, ...req.params };
        return crypto.createHash('md5')
          .update(`posts:${userId}:${JSON.stringify(params)}`)
          .digest('hex');
      }
    });
  }

  userProfile() {
    return this.create({
      ttl: 300, // 5 minutos
      enabled: true,
      keyGenerator: (req) => {
        const userId = req.user?.id || 'anonymous';
        return crypto.createHash('md5')
          .update(`profile:${userId}`)
          .digest('hex');
      }
    });
  }
}

export default new ResponseCacheMiddleware();
