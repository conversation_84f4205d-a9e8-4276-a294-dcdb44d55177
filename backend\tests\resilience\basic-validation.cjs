/**
 * Validação Básica de Resiliência
 * Testes simples e controlados para validar o sistema
 */

const http = require('http');

class BasicValidation {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
  }

  async runBasicTests() {
    console.log('🔍 VALIDAÇÃO BÁSICA DE RESILIÊNCIA');
    console.log('==================================');
    console.log('');

    const results = [];

    // 1. Teste de conectividade
    console.log('1️⃣ Teste de Conectividade');
    const connectivityResult = await this.testConnectivity();
    results.push(connectivityResult);
    this.printResult(connectivityResult);
    console.log('');

    // 2. Teste de autenticação válida
    console.log('2️⃣ Teste de Autenticação Válida');
    const authResult = await this.testValidAuth();
    results.push(authResult);
    this.printResult(authResult);
    console.log('');

    // 3. Teste de carga leve
    console.log('3️⃣ Teste de Carga Leve (10 requests)');
    const lightLoadResult = await this.testLightLoad();
    results.push(lightLoadResult);
    this.printResult(lightLoadResult);
    console.log('');

    // 4. Teste de recuperação
    console.log('4️⃣ Teste de Recuperação');
    const recoveryResult = await this.testRecovery();
    results.push(recoveryResult);
    this.printResult(recoveryResult);
    console.log('');

    // Resumo final
    this.printSummary(results);

    return results;
  }

  async testConnectivity() {
    try {
      const response = await this.makeRequest('/health', 'GET');
      
      return {
        test: 'Conectividade',
        success: response.status === 200,
        status: response.status,
        message: response.status === 200 ? 'Servidor respondendo' : 'Servidor com problemas'
      };
    } catch (error) {
      return {
        test: 'Conectividade',
        success: false,
        error: error.message,
        message: 'Servidor não acessível'
      };
    }
  }

  async testValidAuth() {
    try {
      const response = await this.makeRequest('/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'LoadTest123!'
      });
      
      const success = response.status === 200;
      
      return {
        test: 'Autenticação Válida',
        success,
        status: response.status,
        message: success ? 'Login funcionando' : `Login falhou: ${response.status}`,
        responseTime: response.responseTime
      };
    } catch (error) {
      return {
        test: 'Autenticação Válida',
        success: false,
        error: error.message,
        message: 'Erro na autenticação'
      };
    }
  }

  async testLightLoad() {
    const requests = 10;
    const results = [];
    const startTime = Date.now();

    console.log(`   Enviando ${requests} requests sequenciais...`);

    for (let i = 0; i < requests; i++) {
      try {
        const response = await this.makeRequest('/auth/login', 'POST', {
          email: `loadtest${(i % 5) + 1}@code2post.com`,
          password: 'LoadTest123!'
        });
        
        results.push({
          success: response.status === 200,
          status: response.status,
          responseTime: response.responseTime
        });
        
      } catch (error) {
        results.push({
          success: false,
          error: error.message
        });
      }

      // Pequena pausa entre requests
      await this.sleep(100);
    }

    const duration = Date.now() - startTime;
    const successful = results.filter(r => r.success).length;
    const failed = requests - successful;
    const avgResponseTime = results
      .filter(r => r.responseTime)
      .reduce((sum, r) => sum + r.responseTime, 0) / Math.max(1, successful);

    return {
      test: 'Carga Leve',
      success: successful > 0,
      totalRequests: requests,
      successful,
      failed,
      successRate: Math.round((successful / requests) * 100),
      avgResponseTime: Math.round(avgResponseTime),
      duration,
      message: `${successful}/${requests} requests bem-sucedidos`
    };
  }

  async testRecovery() {
    try {
      // Fazer uma requisição normal
      const beforeResponse = await this.makeRequest('/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'LoadTest123!'
      });

      // Simular uma pequena sobrecarga
      const overloadPromises = [];
      for (let i = 0; i < 20; i++) {
        overloadPromises.push(
          this.makeRequest('/auth/login', 'POST', {
            email: `loadtest${i + 1}@code2post.com`,
            password: 'LoadTest123!'
          }).catch(() => ({ status: 'error' }))
        );
      }

      await Promise.allSettled(overloadPromises);

      // Aguardar um pouco
      await this.sleep(2000);

      // Testar se sistema se recuperou
      const afterResponse = await this.makeRequest('/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'LoadTest123!'
      });

      const recovered = afterResponse.status === 200;

      return {
        test: 'Recuperação',
        success: recovered,
        beforeStatus: beforeResponse.status,
        afterStatus: afterResponse.status,
        message: recovered ? 'Sistema se recuperou após sobrecarga' : 'Sistema não se recuperou'
      };

    } catch (error) {
      return {
        test: 'Recuperação',
        success: false,
        error: error.message,
        message: 'Erro no teste de recuperação'
      };
    }
  }

  async makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const postData = data ? JSON.stringify(data) : '';
      
      const options = {
        hostname: 'localhost',
        port: 3001,
        path,
        method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'BasicValidation'
        },
        timeout: 10000
      };

      if (data) {
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      const req = http.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => { responseData += chunk; });
        res.on('end', () => {
          const responseTime = Date.now() - startTime;
          resolve({
            status: res.statusCode,
            data: responseData,
            responseTime
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (data) {
        req.write(postData);
      }
      
      req.end();
    });
  }

  printResult(result) {
    const icon = result.success ? '✅' : '❌';
    console.log(`   ${icon} ${result.test}: ${result.message}`);
    
    if (result.responseTime) {
      console.log(`      Tempo de resposta: ${result.responseTime}ms`);
    }
    
    if (result.successRate !== undefined) {
      console.log(`      Taxa de sucesso: ${result.successRate}%`);
    }
    
    if (result.error) {
      console.log(`      Erro: ${result.error}`);
    }
  }

  printSummary(results) {
    const successful = results.filter(r => r.success).length;
    const total = results.length;
    
    console.log('📊 RESUMO DA VALIDAÇÃO BÁSICA');
    console.log('=============================');
    console.log(`✅ Testes aprovados: ${successful}/${total}`);
    console.log(`❌ Testes falharam: ${total - successful}/${total}`);
    console.log('');
    
    if (successful === total) {
      console.log('🎉 TODOS OS TESTES BÁSICOS PASSARAM!');
      console.log('✅ Sistema está estável para testes de resiliência mais intensos');
      console.log('');
      console.log('🔄 PRÓXIMOS PASSOS:');
      console.log('   1. Executar testes de carga graduais');
      console.log('   2. Monitorar métricas de sistema');
      console.log('   3. Testar cenários de falha controlados');
    } else {
      console.log('⚠️ ALGUNS TESTES FALHARAM');
      console.log('🔧 Corrija os problemas antes de prosseguir com testes intensos');
      console.log('');
      console.log('🔍 PROBLEMAS IDENTIFICADOS:');
      results.filter(r => !r.success).forEach(result => {
        console.log(`   - ${result.test}: ${result.message || result.error}`);
      });
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const validator = new BasicValidation();
  validator.runBasicTests().catch(console.error);
}

module.exports = BasicValidation;
