# Pre-Production Environment Configuration
# Ambiente idêntico à produção para validação final
NODE_ENV=production
PORT=3001
TESTING=false

# Database Configuration - PostgreSQL para simular produção
DATABASE_URL="postgresql://postgres:code2post123@localhost:5432/code2post_preprod"
POSTGRESQL_URL="postgresql://postgres:code2post123@localhost:5432/code2post_preprod"
# Fallback SQLite para desenvolvimento
SQLITE_URL="file:./prisma/preprod.db"

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=1

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-preproduction-minimum-32-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CSRF Configuration
CSRF_SECRET=your-super-secure-csrf-secret-for-preproduction-minimum-32-characters

# GitHub OAuth Configuration
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GITHUB_CALLBACK_URL=http://localhost:3001/auth/github/callback

# Gemini AI Configuration
GEMINI_API_KEY=your-gemini-api-key

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,https://localhost:3000
CORS_CREDENTIALS=true

# Security Configuration
HELMET_ENABLED=true
RATE_LIMITING_ENABLED=true
CSRF_ENABLED=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/preproduction.log
LOG_RETENTION_DAYS=30

# Monitoring Configuration
METRICS_ENABLED=true
ALERTS_ENABLED=true
ALERT_WEBHOOK_URL=
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_FROM=
ALERT_EMAIL_TO=
ALERT_SMTP_HOST=

# Performance Configuration
CONNECTION_POOL_SIZE=20
QUERY_TIMEOUT=10000
CACHE_TTL=3600

# Feature Flags
FEATURE_GITHUB_INTEGRATION=true
FEATURE_GEMINI_AI=true
FEATURE_ANALYTICS=true
FEATURE_RATE_LIMITING_BY_USER=true

# SSL Configuration (para HTTPS)
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# Session Configuration
SESSION_SECRET=your-super-secure-session-secret-for-preproduction
SESSION_MAX_AGE=86400000

# Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,text/plain

# API Rate Limits (produção)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX=20
RATE_LIMIT_API_MAX=50

# Circuit Breaker Configuration
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60000

# Retry Policy Configuration
RETRY_ENABLED=true
RETRY_MAX_ATTEMPTS=3
RETRY_BASE_DELAY=1000

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=7

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90

# Notification Configuration
NOTIFICATIONS_ENABLED=false
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=

# Development Tools (desabilitados em pré-produção)
DEBUG_ENABLED=false
SWAGGER_ENABLED=false
PROFILING_ENABLED=false
