/**
 * Teste de Carga com PostgreSQL
 * Valida performance com 30+ usuários simultâneos
 */

const http = require('http');
const { performance } = require('perf_hooks');

class PostgreSQLLoadTester {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.results = {
      phases: [],
      summary: {},
      errors: []
    };
  }

  /**
   * Executar testes de carga completos
   */
  async runLoadTests() {
    console.log('🐘 TESTES DE CARGA COM POSTGRESQL');
    console.log('=================================');
    console.log('');

    // Verificar se PostgreSQL está ativo
    await this.validatePostgreSQL();

    // Fases de teste progressivas
    const phases = [
      { name: 'Aquecimento', users: 5, duration: 30000 },
      { name: 'Carga Baixa', users: 10, duration: 60000 },
      { name: 'Carga Média', users: 20, duration: 90000 },
      { name: 'Carga Alta', users: 30, duration: 120000 },
      { name: 'Carga Pico', users: 50, duration: 60000 }
    ];

    for (const phase of phases) {
      console.log(`🧪 Executando: ${phase.name} (${phase.users} usuários, ${phase.duration/1000}s)`);
      const result = await this.runPhase(phase);
      this.results.phases.push(result);
      
      // Aguardar recuperação entre fases
      console.log('⏳ Aguardando recuperação...');
      await this.sleep(10000);
    }

    this.generateReport();
  }

  /**
   * Validar conexão PostgreSQL
   */
  async validatePostgreSQL() {
    console.log('🔍 Validando PostgreSQL...');
    
    try {
      const response = await this.makeRequest('/health', 'GET');
      if (response.status !== 200) {
        throw new Error(`Health check falhou: ${response.status}`);
      }
      
      console.log('   ✅ PostgreSQL conectado e funcionando');
    } catch (error) {
      console.error('   ❌ Erro na validação:', error.message);
      throw new Error('PostgreSQL não está disponível');
    }
  }

  /**
   * Executar uma fase de teste
   */
  async runPhase(phase) {
    const startTime = Date.now();
    const endTime = startTime + phase.duration;
    const results = {
      name: phase.name,
      users: phase.users,
      duration: phase.duration,
      requests: [],
      stats: {
        total: 0,
        successful: 0,
        failed: 0,
        avgLatency: 0,
        maxLatency: 0,
        minLatency: Infinity,
        rps: 0,
        errorRate: 0
      }
    };

    // Executar requests simultâneos
    const promises = [];
    for (let i = 0; i < phase.users; i++) {
      promises.push(this.runUserSimulation(i, endTime, results));
    }

    await Promise.allSettled(promises);

    // Calcular estatísticas finais
    this.calculateStats(results);
    
    const successRate = (results.stats.successful / results.stats.total * 100).toFixed(1);
    console.log(`   📊 Requests: ${results.stats.total}`);
    console.log(`   ✅ Sucessos: ${results.stats.successful} (${successRate}%)`);
    console.log(`   ❌ Falhas: ${results.stats.failed}`);
    console.log(`   ⏱️ Latência: ${results.stats.avgLatency.toFixed(0)}ms (max: ${results.stats.maxLatency.toFixed(0)}ms)`);
    console.log(`   📈 RPS: ${results.stats.rps.toFixed(2)}`);

    return results;
  }

  /**
   * Simular um usuário fazendo requests
   */
  async runUserSimulation(userId, endTime, results) {
    while (Date.now() < endTime) {
      try {
        const requestStart = performance.now();
        
        // Alternar entre diferentes endpoints
        const endpoints = [
          '/health',
          '/auth/login',
          '/api/github/repositories',
          '/csrf-token'
        ];
        
        const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
        const method = endpoint === '/auth/login' ? 'POST' : 'GET';
        const data = endpoint === '/auth/login' ? {
          email: `loadtest${userId}@code2post.com`,
          password: 'LoadTest123!'
        } : null;

        const response = await this.makeRequest(endpoint, method, data);
        const requestEnd = performance.now();
        const latency = requestEnd - requestStart;

        results.requests.push({
          userId,
          endpoint,
          success: response.status < 400,
          status: response.status,
          latency,
          timestamp: Date.now()
        });

        results.stats.total++;
        if (response.status < 400) {
          results.stats.successful++;
        } else {
          results.stats.failed++;
        }

        // Intervalo entre requests (simular uso real)
        await this.sleep(Math.random() * 2000 + 1000); // 1-3 segundos

      } catch (error) {
        results.requests.push({
          userId,
          success: false,
          error: error.message,
          timestamp: Date.now()
        });
        results.stats.total++;
        results.stats.failed++;
      }
    }
  }

  /**
   * Calcular estatísticas da fase
   */
  calculateStats(results) {
    const successfulRequests = results.requests.filter(r => r.success && r.latency);
    
    if (successfulRequests.length > 0) {
      const latencies = successfulRequests.map(r => r.latency);
      results.stats.avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
      results.stats.maxLatency = Math.max(...latencies);
      results.stats.minLatency = Math.min(...latencies);
    }

    results.stats.rps = results.stats.total / (results.duration / 1000);
    results.stats.errorRate = (results.stats.failed / results.stats.total * 100);
  }

  /**
   * Fazer requisição HTTP
   */
  async makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const postData = data ? JSON.stringify(data) : '';
      
      const options = {
        hostname: 'localhost',
        port: 3001,
        path,
        method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PostgreSQLLoadTest'
        },
        timeout: 10000
      };

      if (data) {
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      const req = http.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => { responseData += chunk; });
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (data) {
        req.write(postData);
      }
      
      req.end();
    });
  }

  /**
   * Gerar relatório dos testes
   */
  generateReport() {
    console.log('');
    console.log('📊 RELATÓRIO DE TESTES COM POSTGRESQL');
    console.log('====================================');

    let totalRequests = 0;
    let totalSuccessful = 0;
    let totalFailed = 0;
    let avgRPS = 0;

    this.results.phases.forEach(phase => {
      totalRequests += phase.stats.total;
      totalSuccessful += phase.stats.successful;
      totalFailed += phase.stats.failed;
      avgRPS += phase.stats.rps;

      const successRate = (phase.stats.successful / phase.stats.total * 100).toFixed(1);
      const status = successRate >= 90 ? '✅' : successRate >= 70 ? '⚠️' : '❌';

      console.log(`\n${status} ${phase.name}:`);
      console.log(`   👥 Usuários: ${phase.users}`);
      console.log(`   📊 Requests: ${phase.stats.total}`);
      console.log(`   ✅ Taxa de sucesso: ${successRate}%`);
      console.log(`   ⏱️ Latência média: ${phase.stats.avgLatency.toFixed(0)}ms`);
      console.log(`   📈 RPS: ${phase.stats.rps.toFixed(2)}`);
    });

    // Resumo geral
    const overallSuccessRate = (totalSuccessful / totalRequests * 100).toFixed(1);
    avgRPS = avgRPS / this.results.phases.length;

    console.log(`\n📈 RESUMO GERAL:`);
    console.log(`   📊 Total de requests: ${totalRequests}`);
    console.log(`   ✅ Taxa de sucesso geral: ${overallSuccessRate}%`);
    console.log(`   📈 RPS médio: ${avgRPS.toFixed(2)}`);
    console.log(`   🐘 PostgreSQL: ${overallSuccessRate >= 85 ? 'APROVADO' : 'NECESSITA OTIMIZAÇÃO'}`);

    // Salvar relatório
    this.saveReport();

    // Recomendações
    this.generateRecommendations(overallSuccessRate);
  }

  /**
   * Salvar relatório em arquivo
   */
  saveReport() {
    const reportPath = `postgresql-load-report-${Date.now()}.json`;
    const report = {
      timestamp: new Date().toISOString(),
      database: 'PostgreSQL',
      results: this.results
    };

    try {
      const fs = require('fs');
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n📁 Relatório salvo: ${reportPath}`);
    } catch (error) {
      console.error(`❌ Erro ao salvar relatório: ${error.message}`);
    }
  }

  /**
   * Gerar recomendações baseadas nos resultados
   */
  generateRecommendations(successRate) {
    console.log(`\n💡 RECOMENDAÇÕES:`);
    
    if (successRate >= 90) {
      console.log('   ✅ PostgreSQL está performando excelentemente');
      console.log('   🚀 Sistema pronto para produção');
    } else if (successRate >= 75) {
      console.log('   ⚠️ PostgreSQL tem performance aceitável mas pode melhorar');
      console.log('   🔧 Considere otimizar queries e índices');
      console.log('   🔧 Verifique configurações de connection pool');
    } else {
      console.log('   ❌ PostgreSQL precisa de otimizações urgentes');
      console.log('   🚨 Não recomendado para produção no estado atual');
      console.log('   🔧 Otimize queries, índices e configurações');
      console.log('   🔧 Considere aumentar recursos do servidor');
    }
  }

  /**
   * Utilitário para sleep
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const tester = new PostgreSQLLoadTester();
  tester.runLoadTests().catch(console.error);
}

module.exports = { PostgreSQLLoadTester };
