/**
 * Serviço de Alertas Integrado com Slack, Discord, PagerDuty e Email
 * Implementa notificações em tempo real para falhas críticas
 */

import axios from 'axios';
import loggerService from './loggerService.js';

class IntegratedAlertService {
  constructor() {
    this.channels = {
      slack: {
        enabled: !!process.env.SLACK_WEBHOOK_URL,
        webhookUrl: process.env.SLACK_WEBHOOK_URL,
        channel: process.env.SLACK_CHANNEL || '#alerts',
        username: process.env.SLACK_USERNAME || 'Code2Post Monitor'
      },
      discord: {
        enabled: !!process.env.DISCORD_WEBHOOK_URL,
        webhookUrl: process.env.DISCORD_WEBHOOK_URL,
        username: process.env.DISCORD_USERNAME || 'Code2Post Monitor'
      },
      pagerDuty: {
        enabled: process.env.PAGERDUTY_ENABLED === 'true',
        integrationKey: process.env.PAGERDUTY_INTEGRATION_KEY,
        serviceKey: process.env.PAGERDUTY_SERVICE_KEY,
        apiUrl: 'https://events.pagerduty.com/v2/enqueue'
      },
      email: {
        enabled: process.env.ALERT_EMAIL_ENABLED === 'true',
        smtp: {
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT || 587,
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
          }
        },
        from: process.env.ALERT_EMAIL_FROM,
        to: process.env.ALERT_EMAIL_TO?.split(',') || []
      }
    };

    this.severityConfig = {
      CRITICAL: {
        color: '#FF0000',
        emoji: '🚨',
        priority: 1,
        pagerDuty: true,
        channels: ['slack', 'discord', 'pagerDuty', 'email']
      },
      HIGH: {
        color: '#FF6600',
        emoji: '⚠️',
        priority: 2,
        pagerDuty: false,
        channels: ['slack', 'discord', 'email']
      },
      MEDIUM: {
        color: '#FFCC00',
        emoji: '⚡',
        priority: 3,
        pagerDuty: false,
        channels: ['slack', 'discord']
      },
      LOW: {
        color: '#00FF00',
        emoji: 'ℹ️',
        priority: 4,
        pagerDuty: false,
        channels: ['slack']
      }
    };

    this.alertHistory = [];
    this.maxHistorySize = 1000;
    this.rateLimitCache = new Map();

    console.log('🚨 Integrated Alert Service inicializado');
    this.logChannelStatus();
  }

  /**
   * Log do status dos canais configurados
   */
  logChannelStatus() {
    console.log('📢 Canais de alerta configurados:');
    console.log(`   Slack: ${this.channels.slack.enabled ? '✅' : '❌'}`);
    console.log(`   Discord: ${this.channels.discord.enabled ? '✅' : '❌'}`);
    console.log(`   PagerDuty: ${this.channels.pagerDuty.enabled ? '✅' : '❌'}`);
    console.log(`   Email: ${this.channels.email.enabled ? '✅' : '❌'}`);
  }

  /**
   * Enviar alerta para todos os canais apropriados
   */
  async sendAlert(alertData) {
    const {
      type,
      severity = 'MEDIUM',
      title,
      message,
      metadata = {},
      source = 'system'
    } = alertData;

    // Verificar rate limiting
    if (this.isRateLimited(type, severity)) {
      console.log(`⏳ Alerta rate limited: ${type} (${severity})`);
      return false;
    }

    const alert = {
      id: this.generateAlertId(),
      type,
      severity,
      title,
      message,
      metadata,
      source,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      service: 'code2post-api'
    };

    // Adicionar ao histórico
    this.addToHistory(alert);

    // Log do alerta
    loggerService.warn(`Alert: ${severity} - ${title}`, alert);

    // Obter configuração de severidade
    const config = this.severityConfig[severity] || this.severityConfig.MEDIUM;

    // Enviar para canais apropriados
    const results = await Promise.allSettled(
      config.channels.map(channel => this.sendToChannel(channel, alert, config))
    );

    // Log dos resultados
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    console.log(`📤 Alerta enviado: ${successful} sucessos, ${failed} falhas`);

    return { successful, failed, alert };
  }

  /**
   * Enviar alerta para canal específico
   */
  async sendToChannel(channelName, alert, config) {
    const channel = this.channels[channelName];
    if (!channel?.enabled) {
      return false;
    }

    try {
      switch (channelName) {
        case 'slack':
          return await this.sendToSlack(alert, config);
        case 'discord':
          return await this.sendToDiscord(alert, config);
        case 'pagerDuty':
          return await this.sendToPagerDuty(alert, config);
        case 'email':
          return await this.sendToEmail(alert, config);
        default:
          console.warn(`Canal desconhecido: ${channelName}`);
          return false;
      }
    } catch (error) {
      console.error(`❌ Erro ao enviar para ${channelName}:`, error.message);
      throw error;
    }
  }

  /**
   * Enviar alerta para Slack
   */
  async sendToSlack(alert, config) {
    const payload = {
      channel: this.channels.slack.channel,
      username: this.channels.slack.username,
      icon_emoji: config.emoji,
      attachments: [{
        color: config.color,
        title: `${config.emoji} ${alert.title}`,
        text: alert.message,
        fields: [
          {
            title: 'Severidade',
            value: alert.severity,
            short: true
          },
          {
            title: 'Ambiente',
            value: alert.environment,
            short: true
          },
          {
            title: 'Timestamp',
            value: alert.timestamp,
            short: true
          },
          {
            title: 'Tipo',
            value: alert.type,
            short: true
          }
        ],
        footer: 'Code2Post Monitor',
        ts: Math.floor(new Date(alert.timestamp).getTime() / 1000)
      }]
    };

    // Adicionar campos de metadata se existirem
    if (Object.keys(alert.metadata).length > 0) {
      payload.attachments[0].fields.push({
        title: 'Detalhes',
        value: Object.entries(alert.metadata)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n'),
        short: false
      });
    }

    const response = await axios.post(this.channels.slack.webhookUrl, payload);
    console.log('✅ Alerta enviado para Slack');
    return response.status === 200;
  }

  /**
   * Enviar alerta para Discord
   */
  async sendToDiscord(alert, config) {
    const payload = {
      username: this.channels.discord.username,
      embeds: [{
        title: `${config.emoji} ${alert.title}`,
        description: alert.message,
        color: parseInt(config.color.replace('#', ''), 16),
        timestamp: alert.timestamp,
        fields: [
          {
            name: 'Severidade',
            value: alert.severity,
            inline: true
          },
          {
            name: 'Ambiente',
            value: alert.environment,
            inline: true
          },
          {
            name: 'Tipo',
            value: alert.type,
            inline: true
          }
        ],
        footer: {
          text: 'Code2Post Monitor'
        }
      }]
    };

    // Adicionar metadata se existir
    if (Object.keys(alert.metadata).length > 0) {
      payload.embeds[0].fields.push({
        name: 'Detalhes',
        value: Object.entries(alert.metadata)
          .map(([key, value]) => `**${key}:** ${value}`)
          .join('\n'),
        inline: false
      });
    }

    const response = await axios.post(this.channels.discord.webhookUrl, payload);
    console.log('✅ Alerta enviado para Discord');
    return response.status === 204;
  }

  /**
   * Enviar alerta para PagerDuty
   */
  async sendToPagerDuty(alert, config) {
    if (!config.pagerDuty) {
      return false; // Não enviar para PagerDuty se não for crítico
    }

    const payload = {
      routing_key: this.channels.pagerDuty.integrationKey,
      event_action: 'trigger',
      payload: {
        summary: `${alert.title}: ${alert.message}`,
        source: alert.source,
        severity: alert.severity.toLowerCase(),
        timestamp: alert.timestamp,
        component: 'code2post-api',
        group: alert.type,
        class: alert.environment,
        custom_details: {
          ...alert.metadata,
          alert_id: alert.id,
          service: alert.service
        }
      }
    };

    const response = await axios.post(this.channels.pagerDuty.apiUrl, payload);
    console.log('✅ Alerta enviado para PagerDuty');
    return response.status === 202;
  }

  /**
   * Enviar alerta por email
   */
  async sendToEmail(alert, config) {
    // Implementação básica - em produção usar nodemailer
    console.log(`📧 Email alert: ${alert.title} - ${alert.message}`);
    
    // TODO: Implementar nodemailer
    // const transporter = nodemailer.createTransporter(this.channels.email.smtp);
    // await transporter.sendMail({
    //   from: this.channels.email.from,
    //   to: this.channels.email.to,
    //   subject: `[${alert.severity}] ${alert.title}`,
    //   html: this.generateEmailTemplate(alert, config)
    // });
    
    return true;
  }

  /**
   * Verificar rate limiting
   */
  isRateLimited(type, severity) {
    const key = `${type}_${severity}`;
    const now = Date.now();
    const lastSent = this.rateLimitCache.get(key);
    
    // Rate limit baseado na severidade
    const limits = {
      CRITICAL: 60000,  // 1 minuto
      HIGH: 300000,     // 5 minutos
      MEDIUM: 900000,   // 15 minutos
      LOW: 1800000      // 30 minutos
    };
    
    const limit = limits[severity] || limits.MEDIUM;
    
    if (lastSent && (now - lastSent) < limit) {
      return true;
    }
    
    this.rateLimitCache.set(key, now);
    return false;
  }

  /**
   * Gerar ID único para alerta
   */
  generateAlertId() {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Adicionar alerta ao histórico
   */
  addToHistory(alert) {
    this.alertHistory.unshift(alert);
    
    if (this.alertHistory.length > this.maxHistorySize) {
      this.alertHistory = this.alertHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Obter histórico de alertas
   */
  getHistory(limit = 50) {
    return this.alertHistory.slice(0, limit);
  }

  /**
   * Obter estatísticas de alertas
   */
  getStats() {
    const now = Date.now();
    const last24h = now - (24 * 60 * 60 * 1000);
    const last1h = now - (60 * 60 * 1000);
    
    const recent24h = this.alertHistory.filter(a => 
      new Date(a.timestamp).getTime() >= last24h
    );
    const recent1h = this.alertHistory.filter(a => 
      new Date(a.timestamp).getTime() >= last1h
    );
    
    const bySeverity = {};
    const byType = {};
    
    recent24h.forEach(alert => {
      bySeverity[alert.severity] = (bySeverity[alert.severity] || 0) + 1;
      byType[alert.type] = (byType[alert.type] || 0) + 1;
    });
    
    return {
      total: this.alertHistory.length,
      last24h: recent24h.length,
      last1h: recent1h.length,
      bySeverity,
      byType,
      channels: {
        slack: this.channels.slack.enabled,
        discord: this.channels.discord.enabled,
        pagerDuty: this.channels.pagerDuty.enabled,
        email: this.channels.email.enabled
      }
    };
  }

  /**
   * Testar todos os canais
   */
  async testAllChannels() {
    console.log('🧪 Testando todos os canais de alerta...');
    
    const testAlert = {
      type: 'TEST',
      severity: 'LOW',
      title: 'Teste de Conectividade',
      message: 'Este é um teste dos canais de alerta do Code2Post',
      metadata: {
        test: true,
        timestamp: new Date().toISOString()
      },
      source: 'test'
    };
    
    const result = await this.sendAlert(testAlert);
    console.log('✅ Teste de canais concluído:', result);
    
    return result;
  }
}

export default new IntegratedAlertService();
