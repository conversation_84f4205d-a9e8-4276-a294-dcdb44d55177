/**
 * Configuração de variáveis de ambiente para testes
 */

// Configurações específicas para ambiente de teste
process.env.NODE_ENV = 'test';
process.env.TESTING = 'true';

// Secrets para testes (não usar em produção)
process.env.JWT_SECRET = 'test-jwt-secret-key-minimum-32-characters-long';
process.env.CSRF_SECRET = 'test-csrf-secret-key-minimum-32-characters-long';

// Banco de dados de teste
process.env.DATABASE_URL = 'file:./prisma/test.db';

// Redis de teste (DB separado)
process.env.REDIS_URL = 'redis://localhost:6379/15';

// Configurações de rate limiting para testes (mais permissivas)
process.env.RATE_LIMIT_WINDOW_MS = '60000'; // 1 minuto
process.env.RATE_LIMIT_MAX_REQUESTS = '1000'; // 1000 requests
process.env.RATE_LIMIT_AUTH_MAX = '100'; // 100 requests de auth

// Configurações de circuit breaker para testes
process.env.CIRCUIT_BREAKER_ENABLED = 'true';
process.env.CIRCUIT_BREAKER_FAILURE_THRESHOLD = '10'; // Mais tolerante
process.env.CIRCUIT_BREAKER_RECOVERY_TIMEOUT = '5000'; // 5 segundos

// Configurações de retry para testes
process.env.RETRY_ENABLED = 'true';
process.env.RETRY_MAX_ATTEMPTS = '2'; // Menos tentativas para testes rápidos
process.env.RETRY_BASE_DELAY = '100'; // Delay menor

// Configurações de monitoramento para testes
process.env.METRICS_ENABLED = 'true';
process.env.ALERTS_ENABLED = 'false'; // Desabilitar alertas em testes

// Configurações de logging para testes
process.env.LOG_LEVEL = 'error'; // Apenas erros durante testes

// Configurações de CORS para testes
process.env.CORS_ORIGIN = 'http://localhost:3000';
process.env.CORS_CREDENTIALS = 'true';

// Configurações de segurança para testes
process.env.HELMET_ENABLED = 'true';
process.env.CSRF_ENABLED = 'true';

// Configurações de APIs externas para testes (usar mocks)
process.env.GITHUB_CLIENT_ID = 'test-github-client-id';
process.env.GITHUB_CLIENT_SECRET = 'test-github-client-secret';
process.env.GEMINI_API_KEY = 'test-gemini-api-key';

// Configurações de upload para testes
process.env.MAX_FILE_SIZE = '1048576'; // 1MB para testes
process.env.ALLOWED_FILE_TYPES = 'image/jpeg,image/png,text/plain';

// Configurações de sessão para testes
process.env.SESSION_SECRET = 'test-session-secret-key';
process.env.SESSION_MAX_AGE = '3600000'; // 1 hora

// Configurações de backup para testes (desabilitado)
process.env.BACKUP_ENABLED = 'false';

// Configurações de analytics para testes
process.env.ANALYTICS_ENABLED = 'false';

// Configurações de notificações para testes (desabilitado)
process.env.NOTIFICATIONS_ENABLED = 'false';

// Configurações de desenvolvimento para testes
process.env.DEBUG_ENABLED = 'false';
process.env.SWAGGER_ENABLED = 'false';
process.env.PROFILING_ENABLED = 'false';
