/**
 * Testes de Unidade para Session Cache Service
 */

import { jest, describe, test, expect, beforeEach, afterEach } from '@jest/globals';

// Mock do redisService
const mockRedisClient = {
  setEx: jest.fn(),
  get: jest.fn(),
  del: jest.fn(),
  incr: jest.fn(),
  expire: jest.fn(),
  keys: jest.fn(),
  ttl: jest.fn()
};

const mockRedisService = {
  getClient: jest.fn().mockResolvedValue(mockRedisClient),
  isAvailable: jest.fn().mockReturnValue(true)
};

// Mock do módulo redisService
jest.unstable_mockModule('../../src/services/redisService.js', () => ({
  default: mockRedisService
}));

// Importar após mock
const { default: SessionCacheService } = await import('../../src/services/sessionCacheService.js');

describe('SessionCacheService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('setUserSession', () => {
    test('deve armazenar sessão do usuário com sucesso', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');
      
      const userData = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        isActive: true,
        emailVerified: true
      };
      
      const result = await SessionCacheService.setUserSession('user123', userData, 3600);
      
      expect(result).toBe(true);
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        'user:user123',
        3600,
        expect.stringContaining('"id":"user123"')
      );
    });

    test('deve usar TTL padrão quando não especificado', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');
      
      const userData = { id: 'user123', email: '<EMAIL>' };
      
      await SessionCacheService.setUserSession('user123', userData);
      
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        'user:user123',
        86400, // 24 horas padrão
        expect.any(String)
      );
    });

    test('deve retornar false quando Redis não disponível', async () => {
      mockRedisService.getClient.mockResolvedValue(null);
      
      const userData = { id: 'user123', email: '<EMAIL>' };
      const result = await SessionCacheService.setUserSession('user123', userData);
      
      expect(result).toBe(false);
    });

    test('deve retornar false quando ocorre erro', async () => {
      mockRedisClient.setEx.mockRejectedValue(new Error('Redis error'));
      
      const userData = { id: 'user123', email: '<EMAIL>' };
      const result = await SessionCacheService.setUserSession('user123', userData);
      
      expect(result).toBe(false);
    });

    test('deve incluir timestamp de cache', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');
      
      const userData = { id: 'user123', email: '<EMAIL>' };
      
      await SessionCacheService.setUserSession('user123', userData);
      
      const callArgs = mockRedisClient.setEx.mock.calls[0];
      const cachedData = JSON.parse(callArgs[2]);
      
      expect(cachedData.cachedAt).toBeDefined();
      expect(new Date(cachedData.cachedAt)).toBeInstanceOf(Date);
    });
  });

  describe('getUserSession', () => {
    test('deve recuperar sessão do usuário com sucesso', async () => {
      const cachedData = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        cachedAt: new Date().toISOString()
      };
      
      mockRedisClient.get.mockResolvedValue(JSON.stringify(cachedData));
      
      const result = await SessionCacheService.getUserSession('user123');
      
      expect(result).toEqual(cachedData);
      expect(mockRedisClient.get).toHaveBeenCalledWith('user:user123');
    });

    test('deve retornar null quando não há cache', async () => {
      mockRedisClient.get.mockResolvedValue(null);
      
      const result = await SessionCacheService.getUserSession('user123');
      
      expect(result).toBeNull();
    });

    test('deve retornar null quando Redis não disponível', async () => {
      mockRedisService.getClient.mockResolvedValue(null);
      
      const result = await SessionCacheService.getUserSession('user123');
      
      expect(result).toBeNull();
    });

    test('deve retornar null quando ocorre erro', async () => {
      mockRedisClient.get.mockRejectedValue(new Error('Redis error'));
      
      const result = await SessionCacheService.getUserSession('user123');
      
      expect(result).toBeNull();
    });
  });

  describe('invalidateUserSession', () => {
    test('deve invalidar sessão do usuário com sucesso', async () => {
      mockRedisClient.del.mockResolvedValue(1);
      
      const result = await SessionCacheService.invalidateUserSession('user123');
      
      expect(result).toBe(true);
      expect(mockRedisClient.del).toHaveBeenCalledWith('user:user123');
    });

    test('deve retornar false quando Redis não disponível', async () => {
      mockRedisService.getClient.mockResolvedValue(null);
      
      const result = await SessionCacheService.invalidateUserSession('user123');
      
      expect(result).toBe(false);
    });
  });

  describe('setAuthCache', () => {
    test('deve armazenar cache de autenticação', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');
      
      const userData = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'hashedpassword',
        isActive: true
      };
      
      const result = await SessionCacheService.setAuthCache('<EMAIL>', userData, 300);
      
      expect(result).toBe(true);
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        'auth:<EMAIL>',
        300,
        expect.stringContaining('"email":"<EMAIL>"')
      );
    });

    test('deve usar TTL padrão para auth cache', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');
      
      const userData = { id: 'user123', email: '<EMAIL>' };
      
      await SessionCacheService.setAuthCache('<EMAIL>', userData);
      
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        'auth:<EMAIL>',
        300, // 5 minutos padrão
        expect.any(String)
      );
    });
  });

  describe('getAuthCache', () => {
    test('deve recuperar cache de autenticação', async () => {
      const cachedData = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'hashedpassword'
      };
      
      mockRedisClient.get.mockResolvedValue(JSON.stringify(cachedData));
      
      const result = await SessionCacheService.getAuthCache('<EMAIL>');
      
      expect(result).toEqual(cachedData);
      expect(mockRedisClient.get).toHaveBeenCalledWith('auth:<EMAIL>');
    });
  });

  describe('incrementCounter', () => {
    test('deve incrementar contador com sucesso', async () => {
      mockRedisClient.incr.mockResolvedValue(5);
      
      const result = await SessionCacheService.incrementCounter('test-key', 60);
      
      expect(result).toBe(5);
      expect(mockRedisClient.incr).toHaveBeenCalledWith('counter:test-key');
    });

    test('deve definir expiração para novo contador', async () => {
      mockRedisClient.incr.mockResolvedValue(1); // Primeiro incremento
      
      await SessionCacheService.incrementCounter('test-key', 60);
      
      expect(mockRedisClient.expire).toHaveBeenCalledWith('counter:test-key', 60);
    });

    test('deve retornar 1 quando Redis não disponível', async () => {
      mockRedisService.getClient.mockResolvedValue(null);
      
      const result = await SessionCacheService.incrementCounter('test-key');
      
      expect(result).toBe(1);
    });
  });

  describe('getCacheStats', () => {
    test('deve retornar estatísticas do cache', async () => {
      mockRedisClient.keys
        .mockResolvedValueOnce(['user:1', 'user:2']) // userKeys
        .mockResolvedValueOnce(['auth:<EMAIL>']) // authKeys
        .mockResolvedValueOnce(['counter:key1', 'counter:key2']); // counterKeys
      
      const stats = await SessionCacheService.getCacheStats();
      
      expect(stats).toEqual({
        available: true,
        userSessions: 2,
        authCache: 1,
        counters: 2,
        totalKeys: 5,
        timestamp: expect.any(String)
      });
    });

    test('deve retornar disponibilidade false quando Redis não disponível', async () => {
      mockRedisService.getClient.mockResolvedValue(null);
      
      const stats = await SessionCacheService.getCacheStats();
      
      expect(stats).toEqual({ available: false });
    });

    test('deve retornar erro quando ocorre exceção', async () => {
      mockRedisClient.keys.mockRejectedValue(new Error('Redis error'));
      
      const stats = await SessionCacheService.getCacheStats();
      
      expect(stats).toEqual({
        available: false,
        error: 'Redis error'
      });
    });
  });

  describe('warmupCache', () => {
    test('deve pré-aquecer cache com usuários ativos', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');
      
      const activeUsers = [
        { id: 'user1', email: '<EMAIL>' },
        { id: 'user2', email: '<EMAIL>' },
        { id: 'user3', email: '<EMAIL>' }
      ];
      
      const result = await SessionCacheService.warmupCache(activeUsers);
      
      expect(result).toBe(3);
      expect(mockRedisClient.setEx).toHaveBeenCalledTimes(3);
    });

    test('deve contar apenas sucessos no warmup', async () => {
      mockRedisClient.setEx
        .mockResolvedValueOnce('OK')
        .mockRejectedValueOnce(new Error('Redis error'))
        .mockResolvedValueOnce('OK');
      
      const activeUsers = [
        { id: 'user1', email: '<EMAIL>' },
        { id: 'user2', email: '<EMAIL>' },
        { id: 'user3', email: '<EMAIL>' }
      ];
      
      const result = await SessionCacheService.warmupCache(activeUsers);
      
      expect(result).toBe(2); // Apenas 2 sucessos
    });
  });
});
