/**
 * Testes de Unidade para Circuit Breaker
 */

import { jest, describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { CircuitBreaker } from '../../src/middleware/circuitBreaker.js';

describe('CircuitBreaker', () => {
  let circuitBreaker;
  let mockFunction;

  beforeEach(() => {
    circuitBreaker = new CircuitBreaker({
      name: 'test-circuit',
      failureThreshold: 3,
      recoveryTimeout: 1000,
      expectedErrors: ['Expected Error']
    });
    
    mockFunction = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Estado CLOSED (Normal)', () => {
    test('deve executar função com sucesso', async () => {
      mockFunction.mockResolvedValue('success');
      
      const result = await circuitBreaker.execute(mockFunction, 'arg1', 'arg2');
      
      expect(result).toBe('success');
      expect(mockFunction).toHaveBeenCalledWith('arg1', 'arg2');
      expect(circuitBreaker.state).toBe('CLOSED');
      expect(circuitBreaker.failureCount).toBe(0);
    });

    test('deve contar falhas mas manter CLOSED abaixo do threshold', async () => {
      mockFunction.mockRejectedValue(new Error('Test error'));
      
      // Primeira falha
      await expect(circuitBreaker.execute(mockFunction)).rejects.toThrow('Test error');
      expect(circuitBreaker.state).toBe('CLOSED');
      expect(circuitBreaker.failureCount).toBe(1);
      
      // Segunda falha
      await expect(circuitBreaker.execute(mockFunction)).rejects.toThrow('Test error');
      expect(circuitBreaker.state).toBe('CLOSED');
      expect(circuitBreaker.failureCount).toBe(2);
    });

    test('deve resetar contador de falhas após sucesso', async () => {
      mockFunction.mockRejectedValueOnce(new Error('Test error'));
      mockFunction.mockResolvedValue('success');
      
      // Falha
      await expect(circuitBreaker.execute(mockFunction)).rejects.toThrow('Test error');
      expect(circuitBreaker.failureCount).toBe(1);
      
      // Sucesso - deve resetar contador
      const result = await circuitBreaker.execute(mockFunction);
      expect(result).toBe('success');
      expect(circuitBreaker.failureCount).toBe(0);
    });
  });

  describe('Estado OPEN (Falha)', () => {
    beforeEach(async () => {
      // Forçar circuit breaker para estado OPEN
      mockFunction.mockRejectedValue(new Error('Test error'));
      
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(mockFunction);
        } catch (error) {
          // Ignorar erros para setup
        }
      }
      
      expect(circuitBreaker.state).toBe('OPEN');
    });

    test('deve rejeitar requests imediatamente quando OPEN', async () => {
      mockFunction.mockResolvedValue('success'); // Mesmo que função funcionasse
      
      await expect(circuitBreaker.execute(mockFunction))
        .rejects.toThrow("Circuit Breaker 'test-circuit' está OPEN");
      
      expect(mockFunction).not.toHaveBeenCalled();
    });

    test('deve transicionar para HALF_OPEN após timeout', async () => {
      // Aguardar timeout de recuperação
      await testUtils.sleep(1100);
      
      mockFunction.mockResolvedValue('success');
      
      const result = await circuitBreaker.execute(mockFunction);
      expect(result).toBe('success');
      expect(circuitBreaker.state).toBe('CLOSED'); // Deve fechar após sucesso
    });
  });

  describe('Estado HALF_OPEN (Testando)', () => {
    beforeEach(async () => {
      // Forçar para OPEN
      mockFunction.mockRejectedValue(new Error('Test error'));
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(mockFunction);
        } catch (error) {}
      }
      
      // Aguardar timeout para permitir transição para HALF_OPEN
      await testUtils.sleep(1100);
    });

    test('deve fechar circuit após sucesso em HALF_OPEN', async () => {
      mockFunction.mockResolvedValue('success');
      
      const result = await circuitBreaker.execute(mockFunction);
      
      expect(result).toBe('success');
      expect(circuitBreaker.state).toBe('CLOSED');
      expect(circuitBreaker.failureCount).toBe(0);
    });

    test('deve reabrir circuit após falha em HALF_OPEN', async () => {
      mockFunction.mockRejectedValue(new Error('Test error'));
      
      await expect(circuitBreaker.execute(mockFunction))
        .rejects.toThrow('Test error');
      
      expect(circuitBreaker.state).toBe('OPEN');
    });
  });

  describe('Erros Esperados', () => {
    test('não deve contar erros esperados para circuit breaker', async () => {
      mockFunction.mockRejectedValue(new Error('Expected Error'));
      
      // Executar múltiplas vezes com erro esperado
      for (let i = 0; i < 5; i++) {
        await expect(circuitBreaker.execute(mockFunction))
          .rejects.toThrow('Expected Error');
      }
      
      // Circuit deve permanecer CLOSED
      expect(circuitBreaker.state).toBe('CLOSED');
      expect(circuitBreaker.failureCount).toBe(0);
    });

    test('deve contar erros não esperados normalmente', async () => {
      mockFunction.mockRejectedValue(new Error('Unexpected Error'));
      
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(mockFunction);
        } catch (error) {}
      }
      
      expect(circuitBreaker.state).toBe('OPEN');
    });
  });

  describe('Estatísticas', () => {
    test('deve coletar estatísticas corretamente', async () => {
      mockFunction.mockResolvedValueOnce('success1');
      mockFunction.mockResolvedValueOnce('success2');
      mockFunction.mockRejectedValueOnce(new Error('error1'));
      
      await circuitBreaker.execute(mockFunction);
      await circuitBreaker.execute(mockFunction);
      
      try {
        await circuitBreaker.execute(mockFunction);
      } catch (error) {}
      
      const stats = circuitBreaker.getStats();
      
      expect(stats.name).toBe('test-circuit');
      expect(stats.state).toBe('CLOSED');
      expect(stats.totalRequests).toBe(3);
      expect(stats.successfulRequests).toBe(2);
      expect(stats.failedRequests).toBe(1);
      expect(stats.successRate).toBe('66.67%');
    });

    test('deve calcular uptime corretamente', async () => {
      const stats = circuitBreaker.getStats();
      
      expect(stats.uptime).toMatch(/^\d+s$/);
      expect(stats.lastReset).toBeInstanceOf(Date);
    });
  });

  describe('Configurações', () => {
    test('deve usar configurações padrão', () => {
      const defaultCircuit = new CircuitBreaker();
      
      expect(defaultCircuit.name).toBe('default');
      expect(defaultCircuit.failureThreshold).toBe(5);
      expect(defaultCircuit.recoveryTimeout).toBe(60000);
      expect(defaultCircuit.expectedErrors).toEqual([]);
    });

    test('deve aceitar configurações customizadas', () => {
      const customCircuit = new CircuitBreaker({
        name: 'custom',
        failureThreshold: 10,
        recoveryTimeout: 30000,
        expectedErrors: ['Custom Error']
      });
      
      expect(customCircuit.name).toBe('custom');
      expect(customCircuit.failureThreshold).toBe(10);
      expect(customCircuit.recoveryTimeout).toBe(30000);
      expect(customCircuit.expectedErrors).toEqual(['Custom Error']);
    });
  });

  describe('Middleware Express', () => {
    test('deve criar middleware válido', () => {
      const middleware = circuitBreaker.middleware();
      
      expect(typeof middleware).toBe('function');
      expect(middleware.length).toBe(3); // req, res, next
    });

    test('deve responder com 503 quando circuit está OPEN', async () => {
      // Forçar circuit para OPEN
      mockFunction.mockRejectedValue(new Error('Test error'));
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(mockFunction);
        } catch (error) {}
      }
      
      const middleware = circuitBreaker.middleware();
      const mockReq = {};
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };
      const mockNext = jest.fn();
      
      await middleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(503);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Service Temporarily Unavailable'
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
