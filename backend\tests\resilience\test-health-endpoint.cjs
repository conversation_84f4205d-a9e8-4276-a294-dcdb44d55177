/**
 * Teste do endpoint de health para verificar se o problema é geral
 */

const http = require('http');

async function testHealthEndpoint() {
  console.log('🔍 Testando endpoint de health...');
  
  for (let i = 1; i <= 10; i++) {
    try {
      const result = await makeHealthRequest();
      console.log(`Request ${i}: Status ${result.status}`);
      
      if (result.status === 429) {
        console.log('❌ Rate limiting também no health!');
        break;
      }
    } catch (error) {
      console.log(`Request ${i}: Erro - ${error.message}`);
    }
    
    await sleep(100);
  }
}

async function makeHealthRequest() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/health',
      method: 'GET',
      headers: {
        'User-Agent': 'HealthTest'
      },
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.end();
  });
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

testHealthEndpoint().catch(console.error);
