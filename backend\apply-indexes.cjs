/**
 * Script para aplicar índices de performance no banco de dados
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

async function applyIndexes() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 Aplicando índices de performance...');
    
    // Índices para tabela users
    console.log('📊 Criando índices para tabela users...');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_users_email_active ON users(email, isActive)`;
    console.log('✅ idx_users_email_active criado');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_users_active ON users(isActive)`;
    console.log('✅ idx_users_active criado');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(emailVerified)`;
    console.log('✅ idx_users_email_verified criado');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_users_auth_complete ON users(email, isActive, emailVerified)`;
    console.log('✅ idx_users_auth_complete criado');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(lastLoginAt)`;
    console.log('✅ idx_users_last_login criado');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(createdAt)`;
    console.log('✅ idx_users_created_at criado');
    
    // Verificar se tabela posts existe
    const tables = await prisma.$queryRaw`SELECT name FROM sqlite_master WHERE type='table' AND name='posts'`;
    
    if (tables.length > 0) {
      console.log('📊 Criando índices para tabela posts...');
      
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_posts_user_status ON posts(userId, status)`;
      console.log('✅ idx_posts_user_status criado');

      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_posts_status_date ON posts(status, createdAt)`;
      console.log('✅ idx_posts_status_date criado');

      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status)`;
      console.log('✅ idx_posts_status criado');

      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_posts_user_updated ON posts(userId, updatedAt)`;
      console.log('✅ idx_posts_user_updated criado');

      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_posts_platform ON posts(platform)`;
      console.log('✅ idx_posts_platform criado');
    } else {
      console.log('⚠️ Tabela posts não encontrada - pulando índices de posts');
    }
    
    // Índices para analytics
    console.log('📊 Criando índices para analytics...');
    
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_users_created_period ON users(createdAt, isActive)`;
    console.log('✅ idx_users_created_period criado');
    
    if (tables.length > 0) {
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_posts_created_period ON posts(createdAt, status)`;
      console.log('✅ idx_posts_created_period criado');
    }
    
    // Verificar índices criados
    console.log('📋 Verificando índices criados...');
    const indexes = await prisma.$queryRaw`SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'`;
    
    console.log(`✅ Total de índices criados: ${indexes.length}`);
    indexes.forEach(index => {
      console.log(`   - ${index.name}`);
    });
    
    // Analisar tabelas para otimizar estatísticas
    console.log('📊 Analisando tabelas...');
    await prisma.$executeRaw`ANALYZE users`;
    console.log('✅ Tabela users analisada');
    
    if (tables.length > 0) {
      await prisma.$executeRaw`ANALYZE posts`;
      console.log('✅ Tabela posts analisada');
    }
    
    console.log('');
    console.log('🎉 ÍNDICES DE PERFORMANCE APLICADOS COM SUCESSO!');
    console.log('');
    console.log('📈 Benefícios esperados:');
    console.log('   - Redução de 50-70% na latência de autenticação');
    console.log('   - Suporte a 15-20 usuários simultâneos');
    console.log('   - Melhoria na taxa de sucesso para 85%+ com 10 usuários');
    console.log('');
    console.log('🔍 Para verificar o impacto:');
    console.log('   - Execute os testes de resiliência novamente');
    console.log('   - Monitore as métricas de performance');
    console.log('   - Use EXPLAIN QUERY PLAN para verificar uso dos índices');
    
  } catch (error) {
    console.error('❌ Erro ao aplicar índices:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  applyIndexes().catch(console.error);
}

module.exports = { applyIndexes };
