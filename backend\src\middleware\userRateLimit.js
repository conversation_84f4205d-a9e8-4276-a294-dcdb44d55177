/**
 * Rate Limiting por Usuário com Planos Diferenciados
 * Substitui o modelo baseado em IP por limites baseados em userId e plano
 */

import rateLimit from 'express-rate-limit';
import jwt from 'jsonwebtoken';
import redisService from '../services/redisService.js';

// Configurações de planos
const PLAN_LIMITS = {
  free: {
    requests: 100,
    window: 15 * 60 * 1000, // 15 minutos
    burst: 20 // Burst permitido
  },
  premium: {
    requests: 500,
    window: 15 * 60 * 1000, // 15 minutos
    burst: 100
  },
  enterprise: {
    requests: 2000,
    window: 15 * 60 * 1000, // 15 minutos
    burst: 500
  },
  admin: {
    requests: 10000,
    window: 15 * 60 * 1000, // 15 minutos
    burst: 1000
  }
};

// Whitelist de usuários (sem rate limiting)
const WHITELISTED_USERS = new Set([
  // Adicionar IDs de usuários admin/sistema
]);

/**
 * Extrair informações do usuário do token JWT
 */
const extractUserInfo = (req) => {
  try {
    // Tentar extrair do header Authorization
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      return {
        userId: decoded.userId,
        plan: decoded.plan || 'free',
        isAdmin: decoded.isAdmin || false
      };
    }
    
    // Tentar extrair do cookie
    const cookieToken = req.cookies?.accessToken;
    if (cookieToken) {
      const decoded = jwt.verify(cookieToken, process.env.JWT_SECRET);
      return {
        userId: decoded.userId,
        plan: decoded.plan || 'free',
        isAdmin: decoded.isAdmin || false
      };
    }
    
    return null;
  } catch (error) {
    // Token inválido ou expirado
    return null;
  }
};

/**
 * Obter limites baseados no plano do usuário
 */
const getUserLimits = (userInfo) => {
  if (!userInfo) {
    // Usuário não autenticado - usar limites mais restritivos
    return {
      requests: 50,
      window: 15 * 60 * 1000,
      burst: 10
    };
  }
  
  // Verificar whitelist
  if (WHITELISTED_USERS.has(userInfo.userId) || userInfo.isAdmin) {
    return {
      requests: 999999,
      window: 15 * 60 * 1000,
      burst: 999999
    };
  }
  
  return PLAN_LIMITS[userInfo.plan] || PLAN_LIMITS.free;
};

/**
 * Gerar chave única para rate limiting
 */
const generateKey = (req, userInfo) => {
  if (userInfo) {
    return `user:${userInfo.userId}:${req.route?.path || req.path}`;
  }
  
  // Fallback para IP se não autenticado
  const ip = req.ip?.replace(/^::ffff:/, '') || '127.0.0.1';
  return `ip:${ip}:${req.route?.path || req.path}`;
};

/**
 * Store customizado usando Redis
 */
class UserRateLimitStore {
  constructor() {
    this.prefix = 'rate_limit:';
  }
  
  async increment(key, windowMs) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        // Fallback para memória se Redis não disponível
        return this.memoryFallback(key, windowMs);
      }
      
      const fullKey = this.prefix + key;
      const current = await client.incr(fullKey);
      
      if (current === 1) {
        await client.expire(fullKey, Math.ceil(windowMs / 1000));
      }
      
      const ttl = await client.ttl(fullKey);
      const resetTime = new Date(Date.now() + (ttl * 1000));
      
      return {
        totalHits: current,
        resetTime
      };
    } catch (error) {
      console.error('❌ Erro no rate limit store:', error.message);
      return this.memoryFallback(key, windowMs);
    }
  }
  
  memoryFallback(key, windowMs) {
    // Implementação simples em memória como fallback
    if (!this.memoryStore) {
      this.memoryStore = new Map();
    }
    
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Limpar entradas antigas
    for (const [k, v] of this.memoryStore.entries()) {
      if (v.resetTime < now) {
        this.memoryStore.delete(k);
      }
    }
    
    const entry = this.memoryStore.get(key) || {
      count: 0,
      resetTime: now + windowMs
    };
    
    if (entry.resetTime < now) {
      entry.count = 0;
      entry.resetTime = now + windowMs;
    }
    
    entry.count++;
    this.memoryStore.set(key, entry);
    
    return {
      totalHits: entry.count,
      resetTime: new Date(entry.resetTime)
    };
  }
  
  async decrement(key) {
    try {
      const client = await redisService.getClient();
      if (client) {
        const fullKey = this.prefix + key;
        await client.decr(fullKey);
      }
    } catch (error) {
      console.error('❌ Erro ao decrementar rate limit:', error.message);
    }
  }
  
  async resetKey(key) {
    try {
      const client = await redisService.getClient();
      if (client) {
        const fullKey = this.prefix + key;
        await client.del(fullKey);
      } else if (this.memoryStore) {
        this.memoryStore.delete(key);
      }
    } catch (error) {
      console.error('❌ Erro ao resetar rate limit:', error.message);
    }
  }
}

/**
 * Criar middleware de rate limiting por usuário
 */
const createUserRateLimit = (options = {}) => {
  const store = new UserRateLimitStore();
  
  return async (req, res, next) => {
    try {
      // Extrair informações do usuário
      const userInfo = extractUserInfo(req);
      const limits = getUserLimits(userInfo);
      const key = generateKey(req, userInfo);
      
      // Verificar rate limit
      const result = await store.increment(key, limits.window);
      
      // Adicionar headers informativos
      res.set({
        'X-RateLimit-Limit': limits.requests,
        'X-RateLimit-Remaining': Math.max(0, limits.requests - result.totalHits),
        'X-RateLimit-Reset': result.resetTime.toISOString(),
        'X-RateLimit-Plan': userInfo?.plan || 'anonymous'
      });
      
      // Verificar se excedeu limite
      if (result.totalHits > limits.requests) {
        const retryAfter = Math.ceil((result.resetTime - new Date()) / 1000);
        
        res.set('Retry-After', retryAfter);
        
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: `Too many requests. Limit: ${limits.requests} per ${Math.ceil(limits.window / 60000)} minutes`,
          plan: userInfo?.plan || 'anonymous',
          retryAfter,
          resetTime: result.resetTime.toISOString()
        });
      }
      
      next();
    } catch (error) {
      console.error('❌ Erro no rate limiting por usuário:', error.message);
      // Em caso de erro, permitir request (fail open)
      next();
    }
  };
};

/**
 * Rate limiters específicos por tipo de endpoint
 */
const userRateLimiters = {
  // Rate limiting geral
  general: createUserRateLimit({
    name: 'general'
  }),
  
  // Rate limiting para autenticação (mais restritivo)
  auth: createUserRateLimit({
    name: 'auth',
    multiplier: 0.5 // 50% dos limites normais
  }),
  
  // Rate limiting para APIs externas (GitHub, Gemini)
  api: createUserRateLimit({
    name: 'api',
    multiplier: 0.3 // 30% dos limites normais
  }),
  
  // Rate limiting para uploads/posts
  content: createUserRateLimit({
    name: 'content',
    multiplier: 0.2 // 20% dos limites normais
  })
};

/**
 * Middleware para adicionar usuário à whitelist
 */
const addToWhitelist = (userId) => {
  WHITELISTED_USERS.add(userId);
  console.log(`✅ Usuário ${userId} adicionado à whitelist`);
};

/**
 * Middleware para remover usuário da whitelist
 */
const removeFromWhitelist = (userId) => {
  WHITELISTED_USERS.delete(userId);
  console.log(`❌ Usuário ${userId} removido da whitelist`);
};

/**
 * Obter estatísticas de rate limiting
 */
const getRateLimitStats = async () => {
  try {
    const client = await redisService.getClient();
    if (!client) {
      return { available: false };
    }
    
    const keys = await client.keys('rate_limit:*');
    const stats = {
      totalKeys: keys.length,
      userKeys: keys.filter(k => k.includes(':user:')).length,
      ipKeys: keys.filter(k => k.includes(':ip:')).length,
      whitelistedUsers: WHITELISTED_USERS.size,
      planLimits: PLAN_LIMITS
    };
    
    return stats;
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas:', error.message);
    return { error: error.message };
  }
};

export {
  createUserRateLimit,
  userRateLimiters,
  addToWhitelist,
  removeFromWhitelist,
  getRateLimitStats,
  PLAN_LIMITS,
  extractUserInfo
};

export default userRateLimiters;
