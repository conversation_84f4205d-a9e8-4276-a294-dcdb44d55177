/**
 * Script para limpar cache do Redis
 * Remove todas as chaves de rate limiting
 */

async function clearRedisCache() {
  console.log('🧹 Limpando cache do Redis...');
  
  try {
    const redisService = require('../src/services/redisService.js').default;
    
    // Conectar ao Redis
    const client = await redisService.getClient();
    
    if (!client) {
      console.log('❌ Redis não está disponível');
      return;
    }
    
    // Buscar todas as chaves de rate limiting
    const rateLimitKeys = await client.keys('rl:*');
    const limitKeys = await client.keys('limit:*');
    const allKeys = [...rateLimitKeys, ...limitKeys];
    
    console.log(`🔍 Encontradas ${allKeys.length} chaves de rate limiting`);
    
    if (allKeys.length > 0) {
      // Deletar todas as chaves
      await client.del(...allKeys);
      console.log(`✅ ${allKeys.length} chaves removidas`);
    } else {
      console.log('ℹ️ Nenhuma chave de rate limiting encontrada');
    }
    
    // Também limpar possíveis chaves por IP
    const ipKeys = await client.keys('*127.0.0.1*');
    const localhostKeys = await client.keys('*localhost*');
    const ipRelatedKeys = [...ipKeys, ...localhostKeys];
    
    if (ipRelatedKeys.length > 0) {
      console.log(`🔍 Encontradas ${ipRelatedKeys.length} chaves relacionadas a IP`);
      await client.del(...ipRelatedKeys);
      console.log(`✅ ${ipRelatedKeys.length} chaves de IP removidas`);
    }
    
    console.log('✅ Cache do Redis limpo com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao limpar cache do Redis:', error.message);
  }
}

clearRedisCache();
