/**
 * Setup de Usuários para Testes de Resiliência
 * Cria usuários de teste para validar autenticação durante testes de carga
 */

const bcrypt = require('bcryptjs');

async function createTestUsers() {
  console.log('👥 Criando usuários para testes de resiliência...');
  
  try {
    // Importar Prisma
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    console.log('✅ Conectado ao banco de dados');
    
    // Verificar usuários existentes
    const existingUsers = await prisma.user.findMany({
      where: {
        email: {
          startsWith: 'loadtest'
        }
      }
    });
    
    console.log(`📊 Usuários de teste existentes: ${existingUsers.length}`);
    
    // Criar usuários de teste se não existirem
    const usersToCreate = 50; // Criar 50 usuários para testes
    const password = 'LoadTest123!';
    const hashedPassword = await bcrypt.hash(password, 12);
    
    let createdCount = 0;
    let skippedCount = 0;
    
    for (let i = 1; i <= usersToCreate; i++) {
      const email = `loadtest${i}@code2post.com`;
      
      // Verificar se usuário já existe
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });
      
      if (existingUser) {
        skippedCount++;
        continue;
      }
      
      // Criar usuário
      await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          name: `Load Test User ${i}`,
          isActive: true,
          emailVerified: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      
      createdCount++;
      
      // Log a cada 10 usuários
      if (i % 10 === 0) {
        console.log(`📝 Criados ${createdCount} usuários...`);
      }
    }
    
    await prisma.$disconnect();
    
    console.log('');
    console.log('✅ USUÁRIOS DE TESTE CRIADOS COM SUCESSO!');
    console.log(`📊 Novos usuários: ${createdCount}`);
    console.log(`⏭️ Já existiam: ${skippedCount}`);
    console.log(`👥 Total disponível: ${createdCount + skippedCount}`);
    console.log('');
    console.log('🔑 CREDENCIAIS DOS USUÁRIOS DE TESTE:');
    console.log('   Email: <EMAIL> até <EMAIL>');
    console.log('   Senha: LoadTest123!');
    console.log('');
    console.log('🧪 Agora os testes de carga terão usuários válidos para autenticação!');
    
    return {
      created: createdCount,
      skipped: skippedCount,
      total: createdCount + skippedCount,
      password: password
    };
    
  } catch (error) {
    console.error('❌ Erro ao criar usuários de teste:', error.message);
    throw error;
  }
}

async function cleanupTestUsers() {
  console.log('🧹 Removendo usuários de teste...');
  
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    
    // Remover usuários de teste
    const result = await prisma.user.deleteMany({
      where: {
        email: {
          startsWith: 'loadtest'
        }
      }
    });
    
    await prisma.$disconnect();
    
    console.log(`✅ ${result.count} usuários de teste removidos`);
    
    return result.count;
    
  } catch (error) {
    console.error('❌ Erro ao remover usuários de teste:', error.message);
    throw error;
  }
}

async function validateTestUsers() {
  console.log('🔍 Validando usuários de teste...');
  
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    
    // Contar usuários de teste
    const testUsers = await prisma.user.findMany({
      where: {
        email: {
          startsWith: 'loadtest'
        }
      },
      select: {
        email: true,
        name: true,
        isActive: true,
        emailVerified: true
      }
    });
    
    await prisma.$disconnect();
    
    console.log(`📊 Usuários de teste encontrados: ${testUsers.length}`);
    
    if (testUsers.length > 0) {
      console.log('✅ Primeiros 5 usuários:');
      testUsers.slice(0, 5).forEach(user => {
        console.log(`   - ${user.email} (${user.name}) - Ativo: ${user.isActive}`);
      });
    }
    
    return testUsers;
    
  } catch (error) {
    console.error('❌ Erro ao validar usuários de teste:', error.message);
    throw error;
  }
}

// Executar baseado no comando
const command = process.argv[2];

async function main() {
  switch (command) {
    case 'create':
      await createTestUsers();
      break;
    case 'cleanup':
      await cleanupTestUsers();
      break;
    case 'validate':
      await validateTestUsers();
      break;
    default:
      console.log('👥 GERENCIADOR DE USUÁRIOS DE TESTE');
      console.log('==================================');
      console.log('');
      console.log('📋 Comandos disponíveis:');
      console.log('  node setup-test-users.cjs create   - Criar usuários de teste');
      console.log('  node setup-test-users.cjs validate - Validar usuários existentes');
      console.log('  node setup-test-users.cjs cleanup  - Remover usuários de teste');
      console.log('');
      console.log('💡 Recomendação: Execute "create" antes dos testes de resiliência');
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  createTestUsers,
  cleanupTestUsers,
  validateTestUsers
};
