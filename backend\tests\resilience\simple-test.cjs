/**
 * Teste super simples para isolar o problema do 429
 */

const http = require('http');

async function simpleTest() {
  console.log('🔍 TESTE SUPER SIMPLES');
  console.log('======================');
  
  // Teste 1: Health check
  console.log('1️⃣ Testando /health...');
  try {
    const healthResult = await makeRequest('/health', 'GET');
    console.log(`   Status: ${healthResult.status}`);
  } catch (error) {
    console.log(`   Erro: ${error.message}`);
  }
  
  // Teste 2: Root endpoint
  console.log('2️⃣ Testando / ...');
  try {
    const rootResult = await makeRequest('/', 'GET');
    console.log(`   Status: ${rootResult.status}`);
  } catch (error) {
    console.log(`   Erro: ${error.message}`);
  }
  
  // Teste 3: Login com dados válidos
  console.log('3️⃣ Testando /auth/login com dados válidos...');
  try {
    const loginResult = await makeRequest('/auth/login', 'POST', {
      email: '<EMAIL>',
      password: 'LoadTest123!'
    });
    console.log(`   Status: ${loginResult.status}`);
    console.log(`   Response: ${loginResult.data.substring(0, 200)}...`);
  } catch (error) {
    console.log(`   Erro: ${error.message}`);
  }
  
  // Teste 4: Login com dados inválidos
  console.log('4️⃣ Testando /auth/login com dados inválidos...');
  try {
    const invalidLoginResult = await makeRequest('/auth/login', 'POST', {
      email: '<EMAIL>',
      password: 'invalid'
    });
    console.log(`   Status: ${invalidLoginResult.status}`);
  } catch (error) {
    console.log(`   Erro: ${error.message}`);
  }
  
  // Teste 5: Múltiplos logins válidos
  console.log('5️⃣ Testando múltiplos logins válidos...');
  for (let i = 1; i <= 3; i++) {
    try {
      const result = await makeRequest('/auth/login', 'POST', {
        email: `loadtest${i}@code2post.com`,
        password: 'LoadTest123!'
      });
      console.log(`   Login ${i}: Status ${result.status}`);
      
      if (result.status === 429) {
        console.log('❌ ENCONTRADO O 429!');
        console.log(`   Response: ${result.data}`);
        break;
      }
    } catch (error) {
      console.log(`   Login ${i}: Erro - ${error.message}`);
    }
    
    // Pequena pausa
    await sleep(500);
  }
}

async function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : '';
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SimpleTest'
      },
      timeout: 10000
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => { responseData += chunk; });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: responseData,
          headers: res.headers
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(postData);
    }
    
    req.end();
  });
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

simpleTest().catch(console.error);
