/**
 * Validação completa do sistema para produção
 * Verifica se endpoints temporários foram removidos e originais funcionam
 */

const http = require('http');

async function testEndpoint(path, method = 'POST', expectedStatus = null) {
  return new Promise((resolve) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'Test123!'
    });
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Node.js Production Validation v1.0'
      }
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        const result = {
          path,
          method,
          status: res.statusCode,
          success: expectedStatus ? res.statusCode === expectedStatus : res.statusCode < 400,
          response: data
        };
        resolve(result);
      });
    });
    
    req.on('error', (e) => {
      resolve({
        path,
        method,
        status: 'ERROR',
        success: false,
        error: e.message
      });
    });
    
    req.write(postData);
    req.end();
  });
}

async function validateTemporaryEndpointsRemoved() {
  console.log('🔍 Verificando se endpoints temporários foram removidos...');
  
  const temporaryEndpoints = [
    { path: '/auth/simple-login', method: 'POST' },
    { path: '/auth/simple-verify', method: 'GET' }
  ];
  
  let allRemoved = true;
  
  for (const endpoint of temporaryEndpoints) {
    const result = await testEndpoint(endpoint.path, endpoint.method);
    
    if (result.status === 404 || result.status === 'ERROR') {
      console.log(`✅ ${endpoint.method} ${endpoint.path}: REMOVIDO (${result.status})`);
    } else {
      console.log(`❌ ${endpoint.method} ${endpoint.path}: AINDA EXISTE (${result.status})`);
      allRemoved = false;
    }
  }
  
  return allRemoved;
}

async function validateProductionEndpoints() {
  console.log('🔍 Verificando endpoints de produção...');
  
  const productionEndpoints = [
    { path: '/auth/login', method: 'POST', name: 'Login Principal' },
    { path: '/auth/register', method: 'POST', name: 'Registro' },
    { path: '/auth/logout', method: 'POST', name: 'Logout' },
    { path: '/health', method: 'GET', name: 'Health Check' }
  ];
  
  let allWorking = true;
  
  for (const endpoint of productionEndpoints) {
    const result = await testEndpoint(endpoint.path, endpoint.method);
    
    // Para login, esperamos 200 (sucesso) ou 401/403 (credenciais/CSRF)
    // Para outros, verificamos se não é 404
    const isWorking = endpoint.path === '/auth/login' 
      ? (result.status === 200 || result.status === 401 || result.status === 403)
      : endpoint.path === '/health'
      ? result.status === 200
      : result.status !== 404;
    
    if (isWorking) {
      console.log(`✅ ${endpoint.method} ${endpoint.path} (${endpoint.name}): FUNCIONANDO (${result.status})`);
    } else {
      console.log(`❌ ${endpoint.method} ${endpoint.path} (${endpoint.name}): PROBLEMA (${result.status})`);
      allWorking = false;
    }
  }
  
  return allWorking;
}

async function validateUserService() {
  console.log('🔍 Verificando serviço de usuário refatorado...');
  
  try {
    const userService = require('../src/services/userServicePrisma.js').default;
    
    // Testar inicialização
    await userService.initialize();
    console.log('✅ Inicialização do serviço: OK');
    
    // Testar estatísticas
    const stats = await userService.getServiceStats();
    if (stats && typeof stats.totalUsers === 'number') {
      console.log(`✅ Estatísticas do serviço: OK (${stats.totalUsers} usuários)`);
    } else {
      throw new Error('Estatísticas inválidas');
    }
    
    // Testar validação
    const validation = userService.validate({ email: '<EMAIL>', password: 'Test123!' });
    if (validation.isValid) {
      console.log('✅ Sistema de validação: OK');
    } else {
      throw new Error('Sistema de validação falhou');
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Serviço de usuário: PROBLEMA (${error.message})`);
    return false;
  }
}

async function validateDatabase() {
  console.log('🔍 Verificando conexão com banco de dados...');
  
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    // Testar conexão
    await prisma.$connect();
    console.log('✅ Conexão com banco: OK');
    
    // Testar consulta
    const userCount = await prisma.user.count();
    console.log(`✅ Consulta ao banco: OK (${userCount} usuários)`);
    
    await prisma.$disconnect();
    return true;
  } catch (error) {
    console.log(`❌ Banco de dados: PROBLEMA (${error.message})`);
    return false;
  }
}

async function runProductionValidation() {
  console.log('🚀 VALIDAÇÃO COMPLETA PARA PRODUÇÃO');
  console.log('=====================================');
  console.log('');
  
  const temporaryRemoved = await validateTemporaryEndpointsRemoved();
  console.log('');
  
  const productionWorking = await validateProductionEndpoints();
  console.log('');
  
  const serviceWorking = await validateUserService();
  console.log('');
  
  const databaseWorking = await validateDatabase();
  console.log('');
  
  console.log('📋 RESUMO FINAL:');
  console.log('================');
  console.log(`✅ Endpoints temporários removidos: ${temporaryRemoved ? 'SIM' : 'NÃO'}`);
  console.log(`✅ Endpoints de produção funcionando: ${productionWorking ? 'SIM' : 'NÃO'}`);
  console.log(`✅ Serviço de usuário refatorado: ${serviceWorking ? 'SIM' : 'NÃO'}`);
  console.log(`✅ Banco de dados funcionando: ${databaseWorking ? 'SIM' : 'NÃO'}`);
  console.log('');
  
  if (temporaryRemoved && productionWorking && serviceWorking && databaseWorking) {
    console.log('🎉 SISTEMA PRONTO PARA PRODUÇÃO!');
    console.log('');
    console.log('✅ Todos os endpoints temporários foram removidos');
    console.log('✅ Todos os endpoints de produção estão funcionando');
    console.log('✅ Serviço refatorado está operacional');
    console.log('✅ Banco de dados está acessível');
    console.log('');
    console.log('🚀 Code2Post pode ser deployado com segurança!');
    process.exit(0);
  } else {
    console.log('❌ SISTEMA NÃO ESTÁ PRONTO PARA PRODUÇÃO');
    console.log('');
    console.log('🔧 Corrija os problemas identificados antes do deploy');
    process.exit(1);
  }
}

runProductionValidation();
