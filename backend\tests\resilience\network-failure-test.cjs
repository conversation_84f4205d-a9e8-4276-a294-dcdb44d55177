/**
 * Testes de Falha de Rede
 * Simula latência, perda de pacotes e desconexões temporárias
 */

const http = require('http');
const { performance } = require('perf_hooks');

class NetworkFailureSimulator {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.testResults = [];
    this.scenarios = [
      {
        name: 'Latência Alta (500ms)',
        type: 'latency',
        delay: 500,
        duration: 30000 // 30 segundos
      },
      {
        name: 'Latência Extrema (2000ms)',
        type: 'latency',
        delay: 2000,
        duration: 20000 // 20 segundos
      },
      {
        name: 'Per<PERSON> de Pacotes (10%)',
        type: 'packet_loss',
        lossRate: 0.1,
        duration: 30000
      },
      {
        name: 'Perda de Pacotes (30%)',
        type: 'packet_loss',
        lossRate: 0.3,
        duration: 20000
      },
      {
        name: 'Desconexões Intermitentes',
        type: 'intermittent',
        disconnectRate: 0.2,
        duration: 45000
      },
      {
        name: 'Timeout de Conexão',
        type: 'timeout',
        timeout: 1000,
        duration: 15000
      }
    ];
  }

  /**
   * Executar todos os testes de falha de rede
   */
  async runAllTests() {
    console.log('🌐 TESTES DE FALHA DE REDE');
    console.log('==========================');
    console.log('');

    for (const scenario of this.scenarios) {
      console.log(`🧪 Executando: ${scenario.name}`);
      await this.runScenario(scenario);
      
      // Aguardar recuperação entre testes
      console.log('⏳ Aguardando recuperação do sistema...');
      await this.sleep(10000);
    }

    this.generateReport();
  }

  /**
   * Executar cenário específico
   */
  async runScenario(scenario) {
    const startTime = Date.now();
    const endTime = startTime + scenario.duration;
    const results = {
      scenario: scenario.name,
      type: scenario.type,
      duration: scenario.duration,
      requests: [],
      stats: {
        total: 0,
        successful: 0,
        failed: 0,
        timeouts: 0,
        avgLatency: 0,
        maxLatency: 0,
        minLatency: Infinity
      }
    };

    console.log(`   ⏱️ Duração: ${scenario.duration / 1000}s`);
    console.log(`   🎯 Tipo: ${scenario.type}`);

    // Executar requests durante o período do teste
    while (Date.now() < endTime) {
      try {
        const requestStart = performance.now();
        const response = await this.makeRequestWithFailure(scenario);
        const requestEnd = performance.now();
        const latency = requestEnd - requestStart;

        results.requests.push({
          timestamp: Date.now(),
          success: response.success,
          status: response.status,
          latency,
          error: response.error
        });

        results.stats.total++;
        if (response.success) {
          results.stats.successful++;
        } else {
          results.stats.failed++;
          if (response.error?.includes('timeout')) {
            results.stats.timeouts++;
          }
        }

        // Atualizar estatísticas de latência
        if (response.success) {
          results.stats.maxLatency = Math.max(results.stats.maxLatency, latency);
          results.stats.minLatency = Math.min(results.stats.minLatency, latency);
        }

        // Intervalo entre requests
        await this.sleep(1000);
      } catch (error) {
        console.error(`   ❌ Erro no request: ${error.message}`);
      }
    }

    // Calcular estatísticas finais
    const successfulRequests = results.requests.filter(r => r.success);
    if (successfulRequests.length > 0) {
      results.stats.avgLatency = successfulRequests.reduce((sum, r) => sum + r.latency, 0) / successfulRequests.length;
    }

    if (results.stats.minLatency === Infinity) {
      results.stats.minLatency = 0;
    }

    const successRate = (results.stats.successful / results.stats.total * 100).toFixed(1);
    
    console.log(`   📊 Requests: ${results.stats.total}`);
    console.log(`   ✅ Sucessos: ${results.stats.successful} (${successRate}%)`);
    console.log(`   ❌ Falhas: ${results.stats.failed}`);
    console.log(`   ⏱️ Latência média: ${results.stats.avgLatency.toFixed(0)}ms`);
    console.log(`   📈 Latência máxima: ${results.stats.maxLatency.toFixed(0)}ms`);

    this.testResults.push(results);
  }

  /**
   * Fazer request com simulação de falha
   */
  async makeRequestWithFailure(scenario) {
    return new Promise((resolve) => {
      // Simular perda de pacotes
      if (scenario.type === 'packet_loss' && Math.random() < scenario.lossRate) {
        resolve({
          success: false,
          status: 0,
          error: 'Packet loss simulated'
        });
        return;
      }

      // Simular desconexões intermitentes
      if (scenario.type === 'intermittent' && Math.random() < scenario.disconnectRate) {
        resolve({
          success: false,
          status: 0,
          error: 'Intermittent disconnection simulated'
        });
        return;
      }

      // Configurar timeout
      const timeout = scenario.type === 'timeout' ? scenario.timeout : 10000;

      const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/health',
        method: 'GET',
        headers: {
          'User-Agent': 'NetworkFailureTest'
        },
        timeout
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          // Simular latência adicional
          const delay = scenario.type === 'latency' ? scenario.delay : 0;
          
          setTimeout(() => {
            resolve({
              success: res.statusCode === 200,
              status: res.statusCode,
              data
            });
          }, delay);
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          status: 0,
          error: error.message
        });
      });

      req.on('timeout', () => {
        req.destroy();
        resolve({
          success: false,
          status: 0,
          error: 'Request timeout'
        });
      });

      req.end();
    });
  }

  /**
   * Gerar relatório dos testes
   */
  generateReport() {
    console.log('');
    console.log('📊 RELATÓRIO DE TESTES DE FALHA DE REDE');
    console.log('=======================================');

    let totalScenarios = this.testResults.length;
    let passedScenarios = 0;

    this.testResults.forEach(result => {
      const successRate = (result.stats.successful / result.stats.total * 100);
      const passed = successRate >= 70; // 70% de sucesso mínimo
      
      if (passed) passedScenarios++;

      console.log(`\n🧪 ${result.scenario}`);
      console.log(`   📊 Requests: ${result.stats.total}`);
      console.log(`   ✅ Taxa de sucesso: ${successRate.toFixed(1)}%`);
      console.log(`   ⏱️ Latência média: ${result.stats.avgLatency.toFixed(0)}ms`);
      console.log(`   📈 Latência máxima: ${result.stats.maxLatency.toFixed(0)}ms`);
      console.log(`   ⏰ Timeouts: ${result.stats.timeouts}`);
      console.log(`   🎯 Status: ${passed ? '✅ APROVADO' : '❌ REPROVADO'}`);
    });

    console.log(`\n📈 RESULTADO GERAL: ${passedScenarios}/${totalScenarios} cenários aprovados`);
    console.log(`📊 Taxa de aprovação: ${(passedScenarios/totalScenarios*100).toFixed(1)}%`);

    // Análise de resiliência
    console.log('\n🔍 ANÁLISE DE RESILIÊNCIA:');
    
    const latencyResults = this.testResults.filter(r => r.type === 'latency');
    const packetLossResults = this.testResults.filter(r => r.type === 'packet_loss');
    const intermittentResults = this.testResults.filter(r => r.type === 'intermittent');
    const timeoutResults = this.testResults.filter(r => r.type === 'timeout');

    if (latencyResults.length > 0) {
      const avgSuccessRate = latencyResults.reduce((sum, r) => 
        sum + (r.stats.successful / r.stats.total * 100), 0) / latencyResults.length;
      console.log(`   🐌 Resiliência à latência: ${avgSuccessRate.toFixed(1)}%`);
    }

    if (packetLossResults.length > 0) {
      const avgSuccessRate = packetLossResults.reduce((sum, r) => 
        sum + (r.stats.successful / r.stats.total * 100), 0) / packetLossResults.length;
      console.log(`   📦 Resiliência à perda de pacotes: ${avgSuccessRate.toFixed(1)}%`);
    }

    if (intermittentResults.length > 0) {
      const avgSuccessRate = intermittentResults.reduce((sum, r) => 
        sum + (r.stats.successful / r.stats.total * 100), 0) / intermittentResults.length;
      console.log(`   🔌 Resiliência a desconexões: ${avgSuccessRate.toFixed(1)}%`);
    }

    if (timeoutResults.length > 0) {
      const avgSuccessRate = timeoutResults.reduce((sum, r) => 
        sum + (r.stats.successful / r.stats.total * 100), 0) / timeoutResults.length;
      console.log(`   ⏰ Resiliência a timeouts: ${avgSuccessRate.toFixed(1)}%`);
    }

    // Recomendações
    console.log('\n💡 RECOMENDAÇÕES:');
    
    const overallSuccessRate = this.testResults.reduce((sum, r) => 
      sum + (r.stats.successful / r.stats.total * 100), 0) / this.testResults.length;

    if (overallSuccessRate >= 80) {
      console.log('   ✅ Sistema demonstra boa resiliência a falhas de rede');
    } else if (overallSuccessRate >= 60) {
      console.log('   ⚠️ Sistema tem resiliência moderada - considere melhorias');
      console.log('   🔧 Sugestões: Implementar retry policies mais agressivas');
      console.log('   🔧 Sugestões: Adicionar circuit breakers para APIs externas');
    } else {
      console.log('   ❌ Sistema tem baixa resiliência a falhas de rede');
      console.log('   🚨 Ação necessária: Implementar timeouts adequados');
      console.log('   🚨 Ação necessária: Melhorar handling de erros de rede');
      console.log('   🚨 Ação necessária: Adicionar retry policies robustas');
    }

    // Salvar relatório
    this.saveReport();
  }

  /**
   * Salvar relatório em arquivo
   */
  saveReport() {
    const reportPath = `tests/resilience/reports/network-failure-report-${Date.now()}.json`;
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalScenarios: this.testResults.length,
        passedScenarios: this.testResults.filter(r => 
          (r.stats.successful / r.stats.total * 100) >= 70
        ).length
      },
      results: this.testResults
    };

    try {
      const fs = require('fs');
      const path = require('path');
      
      // Criar diretório se não existir
      const dir = path.dirname(reportPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n📁 Relatório salvo em: ${reportPath}`);
    } catch (error) {
      console.error(`❌ Erro ao salvar relatório: ${error.message}`);
    }
  }

  /**
   * Utilitário para sleep
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const simulator = new NetworkFailureSimulator();
  simulator.runAllTests().catch(console.error);
}

module.exports = { NetworkFailureSimulator };
