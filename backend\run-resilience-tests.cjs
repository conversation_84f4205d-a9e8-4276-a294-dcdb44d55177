/**
 * Script para executar testes de resiliência com rate limiting ajustado
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

async function runResilienceTests() {
  console.log('🚀 EXECUTANDO TESTES DE RESILIÊNCIA COM RATE LIMITING SEGURO');
  console.log('============================================================');
  console.log('');

  // 1. Configurar ambiente para testes
  console.log('1️⃣ Configurando ambiente para testes...');
  await setTestingEnvironment(true);
  
  // 2. Reiniciar servidor com configurações de teste
  console.log('2️⃣ Reiniciando servidor com configurações de teste...');
  await restartServer();
  
  // 3. Aguardar servidor inicializar
  console.log('3️⃣ Aguardando servidor inicializar...');
  await sleep(10000);
  
  // 4. Executar testes básicos
  console.log('4️⃣ Executando testes básicos...');
  await runTest('basic-validation.cjs');
  
  // 5. Executar testes graduais
  console.log('5️⃣ Executando testes de carga gradual...');
  await runTest('gradual-load-test.cjs');
  
  // 6. Restaurar ambiente normal
  console.log('6️⃣ Restaurando ambiente normal...');
  await setTestingEnvironment(false);
  await restartServer();
  
  console.log('');
  console.log('✅ TESTES DE RESILIÊNCIA CONCLUÍDOS!');
  console.log('📊 Verifique os relatórios na pasta tests/resilience/reports/');
}

async function setTestingEnvironment(isTesting) {
  const envPath = path.join(__dirname, '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  // Atualizar variável TESTING
  if (envContent.includes('TESTING=')) {
    envContent = envContent.replace(/TESTING=.*/g, `TESTING=${isTesting}`);
  } else {
    envContent += `\nTESTING=${isTesting}`;
  }
  
  fs.writeFileSync(envPath, envContent);
  console.log(`   ✅ TESTING=${isTesting}`);
}

async function restartServer() {
  return new Promise((resolve) => {
    console.log('   🔄 Reiniciando servidor...');
    
    // Matar processos node existentes
    const killProcess = spawn('taskkill', ['/F', '/IM', 'node.exe'], { 
      stdio: 'ignore',
      shell: true 
    });
    
    killProcess.on('close', () => {
      // Aguardar um pouco antes de iniciar novo servidor
      setTimeout(() => {
        console.log('   ✅ Servidor reiniciado');
        resolve();
      }, 3000);
    });
  });
}

async function runTest(testFile) {
  return new Promise((resolve, reject) => {
    console.log(`   🧪 Executando ${testFile}...`);
    
    const testPath = path.join(__dirname, 'tests', 'resilience', testFile);
    const testProcess = spawn('node', [testPath], {
      stdio: 'pipe',
      cwd: __dirname
    });
    
    let output = '';
    let errorOutput = '';
    
    testProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    testProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log(`   ✅ ${testFile} concluído com sucesso`);
        
        // Salvar output do teste
        const outputPath = path.join(__dirname, 'tests', 'resilience', 'reports', `${testFile}-output.txt`);
        fs.writeFileSync(outputPath, output);
        
        resolve();
      } else {
        console.log(`   ❌ ${testFile} falhou (código ${code})`);
        console.log(`   Erro: ${errorOutput}`);
        reject(new Error(`Test ${testFile} failed with code ${code}`));
      }
    });
    
    // Timeout de 10 minutos por teste
    setTimeout(() => {
      testProcess.kill();
      reject(new Error(`Test ${testFile} timed out`));
    }, 10 * 60 * 1000);
  });
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Executar se chamado diretamente
if (require.main === module) {
  runResilienceTests().catch(error => {
    console.error('❌ Erro nos testes de resiliência:', error.message);
    process.exit(1);
  });
}

module.exports = { runResilienceTests };
