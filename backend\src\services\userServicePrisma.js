import bcrypt from 'bcryptjs';
import { PrismaClient } from '@prisma/client';
import loggerService from './loggerService.js';
import sessionCacheService from './sessionCacheService.js';
import { createPrismaClient } from '../config/database.js';

/**
 * Serviço refatorado para gerenciar usuários usando Prisma ORM
 * Implementa padrão Singleton robusto com gerenciamento de conexão otimizado
 * 
 * @class UserServicePrisma
 * @description Gerencia operações de usuário com Prisma de forma thread-safe
 */
class UserServicePrisma {
  constructor() {
    // Singleton: garantir uma única instância
    if (UserServicePrisma.instance) {
      return UserServicePrisma.instance;
    }
    
    this.prisma = null;
    this.isInitialized = false;
    this.cacheEnabled = true; // Cache habilitado por padrão
    this.initializationPromise = null;
    this.connectionRetries = 0;
    this.maxRetries = 3;
    
    // Configurações de validação
    this.validationRules = {
      email: {
        maxLength: 255,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      },
      password: {
        minLength: 8,
        maxLength: 128,
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
      },
      name: {
        minLength: 2,
        maxLength: 100
      }
    };
    
    // Lista de senhas comuns (básica)
    this.commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];
    
    UserServicePrisma.instance = this;
  }
  
  /**
   * Inicializar conexão com Prisma de forma thread-safe
   * @returns {Promise<PrismaClient>}
   */
  async initialize() {
    // Se já está inicializado, retornar instância existente
    if (this.isInitialized && this.prisma) {
      return this.prisma;
    }
    
    // Se já há uma inicialização em andamento, aguardar
    if (this.initializationPromise) {
      return await this.initializationPromise;
    }
    
    // Iniciar processo de inicialização
    this.initializationPromise = this._performInitialization();
    
    try {
      await this.initializationPromise;
      return this.prisma;
    } finally {
      this.initializationPromise = null;
    }
  }
  
  /**
   * Realizar inicialização real do Prisma
   * @private
   */
  async _performInitialization() {
    try {
      console.log('🔧 UserService: Inicializando conexão Prisma...');
      
      // Usar configuração otimizada do database.js
      this.prisma = createPrismaClient();
      
      // Testar conexão
      await this.prisma.$connect();
      
      // Configurar handlers de desconexão
      this._setupDisconnectionHandlers();
      
      this.isInitialized = true;
      this.connectionRetries = 0;
      
      console.log('✅ UserService: Prisma inicializado com sucesso');
      loggerService.info('UserService Prisma inicializado', {
        service: 'userServicePrisma',
        provider: process.env.DATABASE_PROVIDER || 'sqlite'
      });
      
    } catch (error) {
      console.error('❌ UserService: Erro na inicialização:', error.message);
      
      this.connectionRetries++;
      if (this.connectionRetries < this.maxRetries) {
        console.log(`🔄 Tentativa ${this.connectionRetries}/${this.maxRetries} em 2s...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        return await this._performInitialization();
      }
      
      throw new Error(`Falha na inicialização do UserService após ${this.maxRetries} tentativas: ${error.message}`);
    }
  }
  
  /**
   * Configurar handlers para desconexão limpa
   * @private
   */
  _setupDisconnectionHandlers() {
    const cleanup = async () => {
      if (this.prisma) {
        console.log('🔌 UserService: Desconectando Prisma...');
        await this.prisma.$disconnect();
        this.isInitialized = false;
        this.prisma = null;
      }
    };
    
    process.on('beforeExit', cleanup);
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
  }
  
  /**
   * Obter instância do Prisma (thread-safe)
   * @returns {Promise<PrismaClient>}
   */
  async getPrisma() {
    if (!this.isInitialized || !this.prisma) {
      await this.initialize();
    }
    
    // Verificar se a conexão ainda está ativa
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return this.prisma;
    } catch (error) {
      console.log('⚠️ UserService: Conexão perdida, reconectando...');
      this.isInitialized = false;
      this.prisma = null;
      return await this.initialize();
    }
  }
  
  /**
   * Validar dados de entrada
   * @param {Object} data - Dados para validar
   * @param {string[]} requiredFields - Campos obrigatórios
   * @returns {Object} Resultado da validação
   */
  validate(data, requiredFields = []) {
    const errors = [];
    
    // Verificar campos obrigatórios
    for (const field of requiredFields) {
      if (!data[field]) {
        errors.push(`Campo '${field}' é obrigatório`);
      }
    }
    
    // Validar email
    if (data.email) {
      if (data.email.length > this.validationRules.email.maxLength) {
        errors.push('Email muito longo');
      }
      if (!this.validationRules.email.pattern.test(data.email)) {
        errors.push('Formato de email inválido');
      }
    }
    
    // Validar senha
    if (data.password) {
      if (data.password.length < this.validationRules.password.minLength) {
        errors.push('Senha deve ter pelo menos 8 caracteres');
      }
      if (data.password.length > this.validationRules.password.maxLength) {
        errors.push('Senha muito longa');
      }
      if (!this.validationRules.password.pattern.test(data.password)) {
        errors.push('Senha deve conter: 1 minúscula, 1 maiúscula, 1 número e 1 símbolo (@$!%*?&)');
      }
      if (this.commonPasswords.includes(data.password.toLowerCase())) {
        errors.push('Senha muito comum, escolha uma senha mais segura');
      }
    }
    
    // Validar nome
    if (data.name) {
      if (data.name.length < this.validationRules.name.minLength) {
        errors.push('Nome deve ter pelo menos 2 caracteres');
      }
      if (data.name.length > this.validationRules.name.maxLength) {
        errors.push('Nome muito longo');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Verificar se email já existe
   * @param {string} email 
   * @returns {Promise<boolean>}
   */
  async emailExists(email) {
    try {
      const prisma = await this.getPrisma();
      const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: { id: true }
      });
      return !!user;
    } catch (error) {
      console.error('❌ Erro ao verificar email:', error.message);
      throw new Error('Erro ao verificar disponibilidade do email');
    }
  }
  
  /**
   * Criar hash seguro da senha
   * @param {string} password 
   * @returns {Promise<string>}
   */
  async hashPassword(password) {
    try {
      return await bcrypt.hash(password, 12);
    } catch (error) {
      console.error('❌ Erro ao criar hash da senha:', error.message);
      throw new Error('Erro ao processar senha');
    }
  }
  
  /**
   * Verificar senha
   * @param {string} password 
   * @param {string} hash 
   * @returns {Promise<boolean>}
   */
  async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      console.error('❌ Erro ao verificar senha:', error.message);
      return false;
    }
  }
  
  /**
   * Converter usuário para formato público (sem dados sensíveis)
   * @param {Object} user 
   * @returns {Object}
   */
  toPublicJSON(user) {
    if (!user) return null;
    
    const { password, ...publicUser } = user;
    return {
      ...publicUser,
      createdAt: user.createdAt?.toISOString(),
      updatedAt: user.updatedAt?.toISOString(),
      lastLoginAt: user.lastLoginAt?.toISOString()
    };
  }
  
  /**
   * Obter estatísticas do serviço
   * @returns {Promise<Object>}
   */
  async getServiceStats() {
    try {
      const prisma = await this.getPrisma();
      
      const totalUsers = await prisma.user.count();
      const activeUsers = await prisma.user.count({
        where: { isActive: true }
      });
      const verifiedUsers = await prisma.user.count({
        where: { emailVerified: true }
      });
      
      return {
        totalUsers,
        activeUsers,
        verifiedUsers,
        inactiveUsers: totalUsers - activeUsers,
        unverifiedUsers: totalUsers - verifiedUsers,
        isInitialized: this.isInitialized,
        connectionRetries: this.connectionRetries,
        lastCheck: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Erro ao obter estatísticas:', error.message);
      throw new Error('Erro ao obter estatísticas do serviço');
    }
  }

  /**
   * Criar novo usuário
   * @param {Object} userData - Dados do usuário
   * @returns {Promise<Object>} Usuário criado
   */
  async createUser(userData) {
    try {
      // Validar dados
      const validation = this.validate(userData, ['email', 'password', 'name']);
      if (!validation.isValid) {
        throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
      }

      const { email, password, name, githubId } = userData;
      const normalizedEmail = email.toLowerCase();

      // Verificar se email já existe
      if (await this.emailExists(normalizedEmail)) {
        throw new Error('Email já está em uso');
      }

      // Hash da senha
      const hashedPassword = await this.hashPassword(password);

      const prisma = await this.getPrisma();
      const user = await prisma.user.create({
        data: {
          email: normalizedEmail,
          password: hashedPassword,
          name: name.trim(),
          githubId: githubId ? githubId.toString() : null,
          isActive: true,
          emailVerified: false
        }
      });

      console.log('✅ Usuário criado:', user.email);
      loggerService.info('Usuário criado', {
        userId: user.id,
        email: user.email,
        service: 'userServicePrisma'
      });

      return this.toPublicJSON(user);
    } catch (error) {
      console.error('❌ Erro ao criar usuário:', error.message);
      throw error;
    }
  }

  /**
   * Buscar usuário por ID
   * @param {string} id
   * @returns {Promise<Object|null>}
   */
  async getUserById(id) {
    try {
      // Tentar cache primeiro
      if (this.cacheEnabled) {
        const cached = await sessionCacheService.getUserSession(id);
        if (cached) {
          return cached;
        }
      }

      const prisma = await this.getPrisma();
      const user = await prisma.user.findUnique({
        where: { id }
      });

      const result = user ? this.toPublicJSON(user) : null;

      // Cachear resultado se encontrado
      if (result && this.cacheEnabled) {
        await sessionCacheService.setUserSession(id, result);
      }

      return result;
    } catch (error) {
      console.error('❌ Erro ao buscar usuário por ID:', error.message);
      throw new Error('Erro ao buscar usuário');
    }
  }

  /**
   * Buscar usuário por email
   * @param {string} email
   * @returns {Promise<Object|null>}
   */
  async getUserByEmail(email) {
    try {
      const normalizedEmail = email.toLowerCase();

      // Tentar cache de autenticação primeiro
      if (this.cacheEnabled) {
        const cached = await sessionCacheService.getAuthCache(normalizedEmail);
        if (cached) {
          return cached;
        }
      }

      const prisma = await this.getPrisma();
      const user = await prisma.user.findUnique({
        where: { email: normalizedEmail }
      });

      // Cachear resultado se encontrado
      if (user && this.cacheEnabled) {
        await sessionCacheService.setAuthCache(normalizedEmail, user);
        await sessionCacheService.setUserSession(user.id, user);
      }

      return user || null;
    } catch (error) {
      console.error('❌ Erro ao buscar usuário por email:', error.message);
      throw new Error('Erro ao buscar usuário');
    }
  }

  /**
   * Buscar usuário por GitHub ID
   * @param {string} githubId
   * @returns {Promise<Object|null>}
   */
  async getUserByGitHubId(githubId) {
    try {
      const prisma = await this.getPrisma();
      const user = await prisma.user.findUnique({
        where: { githubId: githubId.toString() }
      });
      return user || null;
    } catch (error) {
      console.error('❌ Erro ao buscar usuário por GitHub ID:', error.message);
      throw new Error('Erro ao buscar usuário');
    }
  }

  /**
   * Atualizar usuário
   * @param {string} id
   * @param {Object} updateData
   * @returns {Promise<Object>}
   */
  async updateUser(id, updateData) {
    try {
      // Validar dados (sem campos obrigatórios para update)
      const validation = this.validate(updateData);
      if (!validation.isValid) {
        throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
      }

      const prisma = await this.getPrisma();

      // Preparar dados para update
      const dataToUpdate = { ...updateData };

      // Hash da senha se fornecida
      if (dataToUpdate.password) {
        dataToUpdate.password = await this.hashPassword(dataToUpdate.password);
      }

      // Normalizar email se fornecido
      if (dataToUpdate.email) {
        dataToUpdate.email = dataToUpdate.email.toLowerCase();

        // Verificar se novo email já existe
        const existingUser = await prisma.user.findUnique({
          where: { email: dataToUpdate.email }
        });
        if (existingUser && existingUser.id !== id) {
          throw new Error('Email já está em uso');
        }
      }

      const user = await prisma.user.update({
        where: { id },
        data: {
          ...dataToUpdate,
          updatedAt: new Date()
        }
      });

      console.log('✅ Usuário atualizado:', user.email);
      loggerService.info('Usuário atualizado', {
        userId: user.id,
        email: user.email,
        service: 'userServicePrisma'
      });

      return this.toPublicJSON(user);
    } catch (error) {
      console.error('❌ Erro ao atualizar usuário:', error.message);
      throw error;
    }
  }

  /**
   * Atualizar último login
   * @param {string} id
   * @returns {Promise<void>}
   */
  async updateLastLogin(id) {
    try {
      const prisma = await this.getPrisma();
      await prisma.user.update({
        where: { id },
        data: { lastLoginAt: new Date() }
      });
      console.log('✅ Último login atualizado para usuário:', id);
    } catch (error) {
      console.error('❌ Erro ao atualizar último login:', error.message);
      // Não lançar erro para não quebrar o fluxo de login
    }
  }

  /**
   * Verificar credenciais de login
   * @param {string} email
   * @param {string} password
   * @returns {Promise<Object>}
   */
  async verifyCredentials(email, password) {
    try {
      console.log('🔍 Verificando credenciais para:', email);

      const user = await this.getUserByEmail(email);
      if (!user) {
        console.log('❌ Usuário não encontrado');
        return { success: false, error: 'Usuário não encontrado' };
      }

      if (!user.isActive) {
        console.log('❌ Usuário inativo');
        return { success: false, error: 'Conta desativada' };
      }

      const isValidPassword = await this.verifyPassword(password, user.password);
      if (!isValidPassword) {
        console.log('❌ Senha incorreta');
        return { success: false, error: 'Senha incorreta' };
      }

      // Atualizar último login
      await this.updateLastLogin(user.id);
      console.log('✅ Credenciais válidas');

      return {
        success: true,
        user: this.toPublicJSON(user)
      };
    } catch (error) {
      console.error('❌ Erro ao verificar credenciais:', error.message);
      return { success: false, error: 'Erro interno do servidor' };
    }
  }

  /**
   * Listar usuários com paginação
   * @param {Object} options - Opções de listagem
   * @returns {Promise<Object>}
   */
  async listUsers(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        isActive = null,
        emailVerified = null
      } = options;

      const skip = (page - 1) * limit;
      const where = {};

      // Filtros
      if (search) {
        where.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          { name: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (isActive !== null) {
        where.isActive = isActive;
      }

      if (emailVerified !== null) {
        where.emailVerified = emailVerified;
      }

      const prisma = await this.getPrisma();

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.user.count({ where })
      ]);

      return {
        users: users.map(user => this.toPublicJSON(user)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('❌ Erro ao listar usuários:', error.message);
      throw new Error('Erro ao listar usuários');
    }
  }

  /**
   * Deletar usuário
   * @param {string} id
   * @returns {Promise<boolean>}
   */
  async deleteUser(id) {
    try {
      const prisma = await this.getPrisma();
      await prisma.user.delete({
        where: { id }
      });

      console.log(`✅ Usuário deletado (ID: ${id})`);
      loggerService.info('Usuário deletado', {
        userId: id,
        service: 'userServicePrisma'
      });

      return true;
    } catch (error) {
      console.error('❌ Erro ao deletar usuário:', error.message);
      throw new Error('Erro ao deletar usuário');
    }
  }
}

// Exportar instância singleton
const userServicePrisma = new UserServicePrisma();
export default userServicePrisma;
