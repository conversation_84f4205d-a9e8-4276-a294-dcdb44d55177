/**
 * Executor de Testes de Stress
 * Testa os limites do sistema com cargas extremas
 */

const http = require('http');
const os = require('os');

class StressTestExecutor {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.metrics = {
      cpu: [],
      memory: [],
      requests: [],
      errors: []
    };
  }

  async execute(testConfig) {
    console.log(`⚡ Iniciando teste de stress: ${testConfig.name}`);
    console.log(`🎯 Tipo: ${testConfig.type}`);
    console.log(`🔥 Intensidade: ${testConfig.intensity}`);
    
    const startTime = Date.now();
    
    // Iniciar monitoramento de sistema
    const monitoringInterval = this.startSystemMonitoring();
    
    try {
      let result;
      
      switch (testConfig.type) {
        case 'cpu':
          result = await this.stressCPU(testConfig);
          break;
        case 'memory':
          result = await this.stressMemory(testConfig);
          break;
        case 'connections':
          result = await this.stressConnections(testConfig);
          break;
        case 'database':
          result = await this.stressDatabase(testConfig);
          break;
        default:
          throw new Error(`Tipo de teste desconhecido: ${testConfig.type}`);
      }
      
      const duration = Date.now() - startTime;
      
      // Parar monitoramento
      clearInterval(monitoringInterval);
      
      return {
        ...result,
        duration,
        systemMetrics: this.calculateSystemMetrics()
      };
      
    } catch (error) {
      clearInterval(monitoringInterval);
      throw error;
    }
  }

  async stressCPU(config) {
    console.log('🔥 Iniciando stress de CPU...');
    
    const duration = config.intensity === 'high' ? 60000 : 30000; // 60s ou 30s
    const concurrentRequests = config.intensity === 'high' ? 500 : 200;
    
    const startTime = Date.now();
    const endTime = startTime + duration;
    
    let totalRequests = 0;
    let successfulRequests = 0;
    let failedRequests = 0;
    
    // Criar múltiplas ondas de requisições simultâneas
    const waves = [];
    
    while (Date.now() < endTime) {
      const wavePromises = [];
      
      for (let i = 0; i < concurrentRequests; i++) {
        wavePromises.push(this.makeCPUIntensiveRequest());
      }
      
      const waveResults = await Promise.allSettled(wavePromises);
      
      totalRequests += waveResults.length;
      successfulRequests += waveResults.filter(r => r.status === 'fulfilled').length;
      failedRequests += waveResults.filter(r => r.status === 'rejected').length;
      
      // Pequena pausa entre ondas
      await this.sleep(100);
    }
    
    return {
      type: 'CPU Stress',
      totalRequests,
      successfulRequests,
      failedRequests,
      errorRate: (failedRequests / totalRequests) * 100,
      avgCPU: this.metrics.cpu.reduce((a, b) => a + b, 0) / this.metrics.cpu.length
    };
  }

  async stressMemory(config) {
    console.log('🧠 Iniciando stress de memória...');
    
    const duration = 45000; // 45 segundos
    const startTime = Date.now();
    const endTime = startTime + duration;
    
    // Criar arrays grandes para consumir memória
    const memoryConsumers = [];
    let totalRequests = 0;
    let successfulRequests = 0;
    
    while (Date.now() < endTime) {
      // Consumir memória gradualmente
      if (config.intensity === 'high') {
        memoryConsumers.push(new Array(100000).fill('memory-stress-test-data'));
      }
      
      // Fazer requisições enquanto consome memória
      try {
        await this.makeMemoryIntensiveRequest();
        successfulRequests++;
      } catch (error) {
        // Continuar mesmo com erros
      }
      
      totalRequests++;
      
      await this.sleep(50);
    }
    
    // Limpar memória
    memoryConsumers.length = 0;
    
    return {
      type: 'Memory Stress',
      totalRequests,
      successfulRequests,
      failedRequests: totalRequests - successfulRequests,
      memoryPeak: Math.max(...this.metrics.memory),
      memoryAvg: this.metrics.memory.reduce((a, b) => a + b, 0) / this.metrics.memory.length
    };
  }

  async stressConnections(config) {
    console.log('🔗 Iniciando stress de conexões...');
    
    const maxConnections = config.intensity === 'extreme' ? 1000 : 500;
    const duration = 30000; // 30 segundos
    
    const connections = [];
    let successfulConnections = 0;
    let failedConnections = 0;
    
    // Criar conexões simultâneas
    for (let i = 0; i < maxConnections; i++) {
      try {
        const connection = this.createPersistentConnection();
        connections.push(connection);
        successfulConnections++;
      } catch (error) {
        failedConnections++;
      }
      
      // Pequena pausa para não sobrecarregar instantaneamente
      if (i % 50 === 0) {
        await this.sleep(10);
      }
    }
    
    // Manter conexões por um tempo
    await this.sleep(duration);
    
    // Fechar conexões
    connections.forEach(conn => {
      try {
        if (conn && conn.destroy) {
          conn.destroy();
        }
      } catch (error) {
        // Ignorar erros ao fechar
      }
    });
    
    return {
      type: 'Connection Stress',
      maxConnections,
      successfulConnections,
      failedConnections,
      connectionSuccessRate: (successfulConnections / maxConnections) * 100
    };
  }

  async stressDatabase(config) {
    console.log('🗄️ Iniciando stress de banco de dados...');
    
    const duration = 60000; // 60 segundos
    const concurrentQueries = config.intensity === 'high' ? 100 : 50;
    
    let totalQueries = 0;
    let successfulQueries = 0;
    let failedQueries = 0;
    
    const startTime = Date.now();
    const endTime = startTime + duration;
    
    while (Date.now() < endTime) {
      const queryPromises = [];
      
      for (let i = 0; i < concurrentQueries; i++) {
        queryPromises.push(this.makeDatabaseIntensiveRequest());
      }
      
      const results = await Promise.allSettled(queryPromises);
      
      totalQueries += results.length;
      successfulQueries += results.filter(r => r.status === 'fulfilled').length;
      failedQueries += results.filter(r => r.status === 'rejected').length;
      
      await this.sleep(200); // Pausa entre ondas
    }
    
    return {
      type: 'Database Stress',
      totalQueries,
      successfulQueries,
      failedQueries,
      querySuccessRate: (successfulQueries / totalQueries) * 100
    };
  }

  async makeCPUIntensiveRequest() {
    return this.makeRequest('/auth/login', {
      email: `stress${Math.random()}@test.com`,
      password: 'StressTest123!'
    });
  }

  async makeMemoryIntensiveRequest() {
    return this.makeRequest('/auth/register', {
      email: `memory${Math.random()}@test.com`,
      password: 'MemoryTest123!',
      name: 'Memory Test User',
      largeData: new Array(1000).fill('data').join('')
    });
  }

  async makeDatabaseIntensiveRequest() {
    return this.makeRequest('/auth/login', {
      email: `db${Math.random()}@test.com`,
      password: 'DatabaseTest123!'
    });
  }

  async makeRequest(path, data) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify(data);
      
      const options = {
        hostname: 'localhost',
        port: 3001,
        path,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData),
          'User-Agent': 'StressTest'
        },
        timeout: 5000
      };
      
      const req = http.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => { responseData += chunk; });
        res.on('end', () => {
          if (res.statusCode < 500) {
            resolve({ status: res.statusCode, data: responseData });
          } else {
            reject(new Error(`HTTP ${res.statusCode}`));
          }
        });
      });
      
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Timeout'));
      });
      
      req.write(postData);
      req.end();
    });
  }

  createPersistentConnection() {
    // Simular conexão persistente com tratamento de erro
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/health',
      method: 'GET',
      timeout: 5000
    });

    // Adicionar handlers de erro para evitar crashes
    req.on('error', () => {
      // Ignorar erros de conexão durante stress test
    });

    req.on('timeout', () => {
      req.destroy();
    });

    return req;
  }

  startSystemMonitoring() {
    return setInterval(() => {
      const cpuUsage = process.cpuUsage();
      const memUsage = process.memoryUsage();
      
      this.metrics.cpu.push(cpuUsage.user + cpuUsage.system);
      this.metrics.memory.push(memUsage.heapUsed / 1024 / 1024); // MB
    }, 1000);
  }

  calculateSystemMetrics() {
    return {
      avgCPU: this.metrics.cpu.reduce((a, b) => a + b, 0) / this.metrics.cpu.length,
      maxCPU: Math.max(...this.metrics.cpu),
      avgMemory: this.metrics.memory.reduce((a, b) => a + b, 0) / this.metrics.memory.length,
      maxMemory: Math.max(...this.metrics.memory),
      samples: this.metrics.cpu.length
    };
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

if (require.main === module) {
  const executor = new StressTestExecutor();
  executor.execute({
    name: 'Teste CPU Standalone',
    type: 'cpu',
    intensity: 'high'
  }).then(result => {
    console.log('📊 Resultado:', result);
  }).catch(console.error);
}

module.exports = {
  execute: async (testConfig) => {
    const executor = new StressTestExecutor();
    return await executor.execute(testConfig);
  }
};
