/**
 * Teste simples para verificar se o erro IPv6 foi corrigido
 */

const http = require('http');

async function testIPv6Fix() {
  console.log('🔍 TESTANDO CORREÇÃO DO ERRO IPv6');
  console.log('=================================');
  
  try {
    // Teste 1: Health check
    console.log('1️⃣ Testando health check...');
    const healthResult = await makeRequest('/health', 'GET');
    console.log(`   Status: ${healthResult.status}`);
    
    // Teste 2: Login
    console.log('2️⃣ Testando login...');
    const loginResult = await makeRequest('/auth/login', 'POST', {
      email: '<EMAIL>',
      password: 'LoadTest123!'
    });
    console.log(`   Status: ${loginResult.status}`);
    
    // Teste 3: <PERSON><PERSON><PERSON><PERSON> requests para testar rate limiting
    console.log('3️⃣ <PERSON>and<PERSON> múl<PERSON> requests...');
    for (let i = 1; i <= 5; i++) {
      const result = await makeRequest('/auth/login', 'POST', {
        email: `loadtest${i}@code2post.com`,
        password: 'LoadTest123!'
      });
      console.log(`   Request ${i}: Status ${result.status}`);
    }
    
    console.log('');
    console.log('✅ TESTE CONCLUÍDO - Nenhum erro IPv6 detectado!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
  }
}

async function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : '';
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'IPv6TestScript'
      },
      timeout: 10000
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => { responseData += chunk; });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(postData);
    }
    
    req.end();
  });
}

testIPv6Fix().catch(console.error);
