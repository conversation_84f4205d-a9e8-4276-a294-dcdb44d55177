/**
 * Teste do Sistema de Alertas Integrados
 */

// Mock das variáveis de ambiente para teste
process.env.SLACK_WEBHOOK_URL = 'https://hooks.slack.com/test';
process.env.DISCORD_WEBHOOK_URL = 'https://discord.com/api/webhooks/test';

// Importar serviço
import('./src/services/integratedAlertService.js').then(async (module) => {
  const alertService = module.default;
  
  console.log('🧪 TESTANDO SISTEMA DE ALERTAS INTEGRADOS');
  console.log('==========================================');
  
  try {
    // Teste 1: Alerta crítico
    console.log('\n1️⃣ Testando alerta CRÍTICO...');
    const criticalAlert = await alertService.sendAlert({
      type: 'SYSTEM_FAILURE',
      severity: 'CRITICAL',
      title: 'Falha Crítica do Sistema',
      message: 'Sistema indisponível - requer ação imediata',
      metadata: {
        errorCode: 'SYS_001',
        affectedUsers: 150,
        downtime: '5 minutos'
      },
      source: 'monitoring'
    });
    
    console.log(`   ✅ Alerta crítico enviado: ${criticalAlert.successful} sucessos, ${criticalAlert.failed} falhas`);
    
    // Teste 2: Alerta de warning
    console.log('\n2️⃣ Testando alerta WARNING...');
    const warningAlert = await alertService.sendAlert({
      type: 'PERFORMANCE_DEGRADATION',
      severity: 'HIGH',
      title: 'Performance Degradada',
      message: 'Latência acima do normal detectada',
      metadata: {
        avgLatency: '1200ms',
        threshold: '800ms',
        endpoint: '/api/posts'
      },
      source: 'performance_monitor'
    });
    
    console.log(`   ✅ Alerta warning enviado: ${warningAlert.successful} sucessos, ${warningAlert.failed} falhas`);
    
    // Teste 3: Alerta informativo
    console.log('\n3️⃣ Testando alerta INFO...');
    const infoAlert = await alertService.sendAlert({
      type: 'DEPLOYMENT',
      severity: 'LOW',
      title: 'Deploy Realizado',
      message: 'Nova versão implantada com sucesso',
      metadata: {
        version: '1.6.1',
        deployTime: '2 minutos',
        environment: 'production'
      },
      source: 'deployment'
    });
    
    console.log(`   ✅ Alerta info enviado: ${infoAlert.successful} sucessos, ${infoAlert.failed} falhas`);
    
    // Teste 4: Rate limiting
    console.log('\n4️⃣ Testando rate limiting...');
    const rateLimitTest1 = await alertService.sendAlert({
      type: 'RATE_LIMIT_TEST',
      severity: 'CRITICAL',
      title: 'Teste Rate Limit 1',
      message: 'Primeiro alerta do mesmo tipo',
      source: 'test'
    });
    
    const rateLimitTest2 = await alertService.sendAlert({
      type: 'RATE_LIMIT_TEST',
      severity: 'CRITICAL',
      title: 'Teste Rate Limit 2',
      message: 'Segundo alerta do mesmo tipo (deve ser bloqueado)',
      source: 'test'
    });
    
    console.log(`   ✅ Primeiro alerta: ${rateLimitTest1.successful} sucessos`);
    console.log(`   ⏳ Segundo alerta: ${rateLimitTest2 ? rateLimitTest2.successful : 0} sucessos (rate limited)`);
    
    // Teste 5: Estatísticas
    console.log('\n5️⃣ Verificando estatísticas...');
    const stats = alertService.getStats();
    console.log(`   📊 Total de alertas: ${stats.total}`);
    console.log(`   📈 Últimas 24h: ${stats.last24h}`);
    console.log(`   📈 Última 1h: ${stats.last1h}`);
    console.log(`   🔧 Canais ativos: ${Object.entries(stats.channels).filter(([,active]) => active).map(([name]) => name).join(', ')}`);
    
    // Teste 6: Histórico
    console.log('\n6️⃣ Verificando histórico...');
    const history = alertService.getHistory(5);
    console.log(`   📚 Últimos ${history.length} alertas:`);
    history.forEach((alert, index) => {
      console.log(`      ${index + 1}. [${alert.severity}] ${alert.title} (${new Date(alert.timestamp).toLocaleTimeString()})`);
    });
    
    console.log('\n✅ TODOS OS TESTES DE ALERTAS CONCLUÍDOS!');
    console.log('==========================================');
    
  } catch (error) {
    console.error('❌ Erro nos testes de alertas:', error.message);
  }
}).catch(console.error);
