/**
 * Serviço de Cache de Sessões com Redis
 * Otimizado para suportar 10+ usuários simultâneos
 */

import redisService from './redisService.js';

class SessionCacheService {
  constructor() {
    this.prefix = 'session:';
    this.userPrefix = 'user:';
    this.defaultTTL = 24 * 60 * 60; // 24 horas
    this.shortTTL = 5 * 60; // 5 minutos para dados temporários
  }

  /**
   * Armazenar dados de sessão do usuário
   */
  async setUserSession(userId, userData, ttl = this.defaultTTL) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        console.warn('⚠️ Redis não disponível - pulando cache de sessão');
        return false;
      }

      const key = `${this.userPrefix}${userId}`;
      const sessionData = {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        isActive: userData.isActive,
        emailVerified: userData.emailVerified,
        lastLoginAt: userData.lastLoginAt,
        preferences: userData.preferences,
        cachedAt: new Date().toISOString()
      };

      await client.setEx(key, ttl, JSON.stringify(sessionData));
      console.log(`✅ Sessão cached para usuário: ${userId}`);
      return true;
    } catch (error) {
      console.error('❌ Erro ao cachear sessão:', error.message);
      return false;
    }
  }

  /**
   * Recuperar dados de sessão do usuário
   */
  async getUserSession(userId) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return null;
      }

      const key = `${this.userPrefix}${userId}`;
      const cached = await client.get(key);
      
      if (cached) {
        const sessionData = JSON.parse(cached);
        console.log(`🎯 Cache hit para usuário: ${userId}`);
        return sessionData;
      }

      console.log(`❌ Cache miss para usuário: ${userId}`);
      return null;
    } catch (error) {
      console.error('❌ Erro ao recuperar sessão do cache:', error.message);
      return null;
    }
  }

  /**
   * Invalidar sessão do usuário
   */
  async invalidateUserSession(userId) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return false;
      }

      const key = `${this.userPrefix}${userId}`;
      await client.del(key);
      console.log(`🗑️ Sessão invalidada para usuário: ${userId}`);
      return true;
    } catch (error) {
      console.error('❌ Erro ao invalidar sessão:', error.message);
      return false;
    }
  }

  /**
   * Cache de dados de autenticação por email
   */
  async setAuthCache(email, userData, ttl = this.shortTTL) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return false;
      }

      const key = `auth:${email}`;
      const authData = {
        id: userData.id,
        email: userData.email,
        password: userData.password, // Hash da senha
        isActive: userData.isActive,
        emailVerified: userData.emailVerified,
        cachedAt: new Date().toISOString()
      };

      await client.setEx(key, ttl, JSON.stringify(authData));
      console.log(`🔐 Auth cached para email: ${email}`);
      return true;
    } catch (error) {
      console.error('❌ Erro ao cachear auth:', error.message);
      return false;
    }
  }

  /**
   * Recuperar dados de autenticação por email
   */
  async getAuthCache(email) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return null;
      }

      const key = `auth:${email}`;
      const cached = await client.get(key);
      
      if (cached) {
        const authData = JSON.parse(cached);
        console.log(`🎯 Auth cache hit para email: ${email}`);
        return authData;
      }

      console.log(`❌ Auth cache miss para email: ${email}`);
      return null;
    } catch (error) {
      console.error('❌ Erro ao recuperar auth do cache:', error.message);
      return null;
    }
  }

  /**
   * Invalidar cache de autenticação
   */
  async invalidateAuthCache(email) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return false;
      }

      const key = `auth:${email}`;
      await client.del(key);
      console.log(`🗑️ Auth cache invalidado para email: ${email}`);
      return true;
    } catch (error) {
      console.error('❌ Erro ao invalidar auth cache:', error.message);
      return false;
    }
  }

  /**
   * Cache de contadores para rate limiting otimizado
   */
  async incrementCounter(key, ttl = 60) {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return 1; // Fallback sem cache
      }

      const counterKey = `counter:${key}`;
      const current = await client.incr(counterKey);
      
      if (current === 1) {
        await client.expire(counterKey, ttl);
      }

      return current;
    } catch (error) {
      console.error('❌ Erro ao incrementar contador:', error.message);
      return 1;
    }
  }

  /**
   * Obter estatísticas de cache
   */
  async getCacheStats() {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return { available: false };
      }

      // Contar chaves por tipo
      const userKeys = await client.keys(`${this.userPrefix}*`);
      const authKeys = await client.keys('auth:*');
      const counterKeys = await client.keys('counter:*');

      const stats = {
        available: true,
        userSessions: userKeys.length,
        authCache: authKeys.length,
        counters: counterKeys.length,
        totalKeys: userKeys.length + authKeys.length + counterKeys.length,
        timestamp: new Date().toISOString()
      };

      return stats;
    } catch (error) {
      console.error('❌ Erro ao obter estatísticas de cache:', error.message);
      return { available: false, error: error.message };
    }
  }

  /**
   * Limpar cache expirado (manutenção)
   */
  async cleanupExpiredCache() {
    try {
      const client = await redisService.getClient();
      if (!client) {
        return false;
      }

      // Redis limpa automaticamente chaves expiradas
      // Mas podemos forçar limpeza de chaves específicas se necessário
      
      console.log('🧹 Limpeza de cache executada');
      return true;
    } catch (error) {
      console.error('❌ Erro na limpeza de cache:', error.message);
      return false;
    }
  }

  /**
   * Pré-aquecer cache com usuários ativos
   */
  async warmupCache(activeUsers) {
    try {
      console.log(`🔥 Pré-aquecendo cache para ${activeUsers.length} usuários...`);
      
      let warmedUp = 0;
      for (const user of activeUsers) {
        const success = await this.setUserSession(user.id, user, this.defaultTTL);
        if (success) warmedUp++;
      }

      console.log(`✅ Cache pré-aquecido: ${warmedUp}/${activeUsers.length} usuários`);
      return warmedUp;
    } catch (error) {
      console.error('❌ Erro no pré-aquecimento:', error.message);
      return 0;
    }
  }
}

export default new SessionCacheService();
