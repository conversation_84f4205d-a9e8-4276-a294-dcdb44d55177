/**
 * Teste específico para verificar bypass do rate limiting
 */

const http = require('http');

async function testRateLimitBypass() {
  console.log('🔍 Testando bypass do rate limiting...');
  
  // Teste 1: Com User-Agent normal (deve ser limitado)
  console.log('1️⃣ Teste com User-Agent normal:');
  const normalResult = await makeTestRequest('Normal-Browser');
  console.log(`   Status: ${normalResult.status}`);
  console.log(`   Headers: ${JSON.stringify(normalResult.headers)}`);
  
  // Teste 2: Com User-Agent de teste (deve passar)
  console.log('2️⃣ Teste com User-Agent de teste:');
  const testResult = await makeTestRequest('BasicValidation');
  console.log(`   Status: ${testResult.status}`);
  console.log(`   Headers: ${JSON.stringify(testResult.headers)}`);
  
  // Teste 3: M<PERSON><PERSON>las requisições com User-Agent de teste
  console.log('3️⃣ Múltiplas requisições com User-Agent de teste:');
  for (let i = 1; i <= 5; i++) {
    const result = await makeTestRequest('LoadTest');
    console.log(`   Request ${i}: Status ${result.status}`);
    
    if (result.status === 429) {
      console.log('❌ Rate limiting ainda ativo!');
      break;
    }
  }
}

async function makeTestRequest(userAgent) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: 'LoadTest123!'
    });
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': userAgent
      },
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: data,
          headers: {
            'x-ratelimit-limit': res.headers['x-ratelimit-limit'],
            'x-ratelimit-remaining': res.headers['x-ratelimit-remaining'],
            'x-ratelimit-reset': res.headers['x-ratelimit-reset']
          }
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.write(postData);
    req.end();
  });
}

testRateLimitBypass().catch(console.error);
