/**
 * Monitor de Sistema em Tempo Real
 * Monitora CPU, memória, conexões e performance durante os testes
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

class SystemMonitor {
  constructor() {
    this.isMonitoring = false;
    this.metrics = [];
    this.startTime = null;
    this.monitoringInterval = null;
    this.alertThresholds = {
      cpu: 80, // %
      memory: 85, // %
      responseTime: 2000, // ms
      errorRate: 10 // %
    };
  }

  start() {
    if (this.isMonitoring) {
      console.log('⚠️ Monitor já está em execução');
      return;
    }

    console.log('📊 Iniciando monitoramento do sistema...');
    this.isMonitoring = true;
    this.startTime = new Date();
    this.metrics = [];

    // Monitorar a cada segundo
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, 1000);

    // Relatório a cada 30 segundos
    this.reportInterval = setInterval(() => {
      this.printCurrentStatus();
    }, 30000);

    console.log('✅ Monitor iniciado - coletando métricas a cada 1s');
  }

  stop() {
    if (!this.isMonitoring) {
      console.log('⚠️ Monitor não está em execução');
      return;
    }

    console.log('🛑 Parando monitoramento...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    if (this.reportInterval) {
      clearInterval(this.reportInterval);
    }

    const report = this.generateFinalReport();
    console.log('✅ Monitoramento parado');
    
    return report;
  }

  collectMetrics() {
    const timestamp = new Date();
    const cpuUsage = process.cpuUsage();
    const memUsage = process.memoryUsage();
    const systemMem = {
      total: os.totalmem(),
      free: os.freemem()
    };

    const metric = {
      timestamp,
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        percent: this.calculateCPUPercent()
      },
      memory: {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
        systemUsed: systemMem.total - systemMem.free,
        systemTotal: systemMem.total,
        systemPercent: ((systemMem.total - systemMem.free) / systemMem.total) * 100
      },
      system: {
        loadAvg: os.loadavg(),
        uptime: os.uptime(),
        platform: os.platform(),
        arch: os.arch()
      }
    };

    this.metrics.push(metric);

    // Verificar alertas
    this.checkAlerts(metric);

    // Manter apenas últimos 1000 registros para evitar uso excessivo de memória
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  calculateCPUPercent() {
    // Aproximação simples do uso de CPU
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    return 100 - ~~(100 * totalIdle / totalTick);
  }

  checkAlerts(metric) {
    const alerts = [];

    // Alerta de CPU
    if (metric.cpu.percent > this.alertThresholds.cpu) {
      alerts.push(`🔥 CPU alta: ${metric.cpu.percent.toFixed(1)}%`);
    }

    // Alerta de memória
    if (metric.memory.systemPercent > this.alertThresholds.memory) {
      alerts.push(`🧠 Memória alta: ${metric.memory.systemPercent.toFixed(1)}%`);
    }

    // Alerta de heap
    const heapPercent = (metric.memory.heapUsed / metric.memory.heapTotal) * 100;
    if (heapPercent > 90) {
      alerts.push(`⚠️ Heap quase cheio: ${heapPercent.toFixed(1)}%`);
    }

    if (alerts.length > 0) {
      console.log(`🚨 ALERTAS: ${alerts.join(' | ')}`);
    }
  }

  printCurrentStatus() {
    if (this.metrics.length === 0) return;

    const latest = this.metrics[this.metrics.length - 1];
    const duration = Math.round((Date.now() - this.startTime) / 1000);

    console.log('');
    console.log('📊 STATUS ATUAL DO SISTEMA');
    console.log('==========================');
    console.log(`⏱️ Tempo de monitoramento: ${duration}s`);
    console.log(`🖥️ CPU: ${latest.cpu.percent.toFixed(1)}%`);
    console.log(`🧠 Memória Sistema: ${latest.memory.systemPercent.toFixed(1)}%`);
    console.log(`📦 Heap: ${(latest.memory.heapUsed / 1024 / 1024).toFixed(1)}MB`);
    console.log(`📈 Load Average: ${latest.system.loadAvg.map(l => l.toFixed(2)).join(', ')}`);
    console.log(`📊 Métricas coletadas: ${this.metrics.length}`);
    console.log('');
  }

  generateFinalReport() {
    if (this.metrics.length === 0) {
      return { error: 'Nenhuma métrica coletada' };
    }

    const duration = this.metrics[this.metrics.length - 1].timestamp - this.metrics[0].timestamp;
    
    // Calcular estatísticas
    const cpuValues = this.metrics.map(m => m.cpu.percent);
    const memoryValues = this.metrics.map(m => m.memory.systemPercent);
    const heapValues = this.metrics.map(m => m.memory.heapUsed / 1024 / 1024);

    const report = {
      monitoringPeriod: {
        start: this.startTime,
        end: new Date(),
        duration: duration,
        samples: this.metrics.length
      },
      cpu: {
        avg: this.average(cpuValues),
        min: Math.min(...cpuValues),
        max: Math.max(...cpuValues),
        p95: this.percentile(cpuValues, 95)
      },
      memory: {
        system: {
          avg: this.average(memoryValues),
          min: Math.min(...memoryValues),
          max: Math.max(...memoryValues),
          p95: this.percentile(memoryValues, 95)
        },
        heap: {
          avg: this.average(heapValues),
          min: Math.min(...heapValues),
          max: Math.max(...heapValues),
          p95: this.percentile(heapValues, 95)
        }
      },
      alerts: this.countAlerts(),
      rawMetrics: this.metrics.slice(-100) // Últimas 100 amostras
    };

    // Salvar relatório
    const reportFile = path.join(__dirname, 'reports', `system-monitor-${Date.now()}.json`);
    
    // Criar diretório se não existir
    const reportsDir = path.dirname(reportFile);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

    console.log('📊 RELATÓRIO FINAL DE MONITORAMENTO');
    console.log('===================================');
    console.log(`📁 Relatório salvo: ${reportFile}`);
    console.log(`⏱️ Duração: ${Math.round(duration / 1000)}s`);
    console.log(`📊 Amostras: ${report.monitoringPeriod.samples}`);
    console.log(`🖥️ CPU - Média: ${report.cpu.avg.toFixed(1)}% | Máx: ${report.cpu.max.toFixed(1)}%`);
    console.log(`🧠 Memória - Média: ${report.memory.system.avg.toFixed(1)}% | Máx: ${report.memory.system.max.toFixed(1)}%`);
    console.log(`📦 Heap - Média: ${report.memory.heap.avg.toFixed(1)}MB | Máx: ${report.memory.heap.max.toFixed(1)}MB`);

    return report;
  }

  countAlerts() {
    let cpuAlerts = 0;
    let memoryAlerts = 0;
    let heapAlerts = 0;

    this.metrics.forEach(metric => {
      if (metric.cpu.percent > this.alertThresholds.cpu) cpuAlerts++;
      if (metric.memory.systemPercent > this.alertThresholds.memory) memoryAlerts++;
      
      const heapPercent = (metric.memory.heapUsed / metric.memory.heapTotal) * 100;
      if (heapPercent > 90) heapAlerts++;
    });

    return { cpuAlerts, memoryAlerts, heapAlerts };
  }

  average(values) {
    return values.reduce((a, b) => a + b, 0) / values.length;
  }

  percentile(values, p) {
    const sorted = values.slice().sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index];
  }
}

// Executar monitor standalone se chamado diretamente
if (require.main === module) {
  const monitor = new SystemMonitor();
  
  console.log('🚀 Iniciando monitor standalone...');
  console.log('Pressione Ctrl+C para parar');
  
  monitor.start();
  
  // Parar monitor ao receber SIGINT
  process.on('SIGINT', () => {
    console.log('\n🛑 Recebido sinal de parada...');
    monitor.stop();
    process.exit(0);
  });
}

module.exports = SystemMonitor;
