/**
 * Teste Rápido de Resiliência
 * Executa uma versão reduzida dos testes para validação inicial
 */

const ResilienceTestSuite = require('./test-suite-manager.cjs');

class QuickResilienceTest extends ResilienceTestSuite {
  async runQuickTests() {
    console.log('⚡ TESTE RÁPIDO DE RESILIÊNCIA');
    console.log('=============================');
    console.log('');
    
    this.startTime = new Date();
    
    try {
      // 1. Teste de Carga Rápido
      console.log('📊 TESTE DE CARGA RÁPIDO');
      console.log('------------------------');
      await this.runQuickLoadTest();
      console.log('');

      // 2. Teste de Stress Básico
      console.log('⚡ TESTE DE STRESS BÁSICO');
      console.log('------------------------');
      await this.runQuickStressTest();
      console.log('');

      // 3. Teste de Falha Simples
      console.log('💥 TESTE DE FALHA SIMPLES');
      console.log('-------------------------');
      await this.runQuickFailureTest();
      console.log('');

      this.endTime = new Date();
      await this.generateQuickReport();
      
    } catch (error) {
      console.error('❌ Erro no teste rápido:', error.message);
      this.endTime = new Date();
      await this.generateErrorReport(error);
    }
  }

  async runQuickLoadTest() {
    const quickTests = [
      { name: 'Carga Baixa', users: 5, duration: 30 },
      { name: 'Carga Média', users: 20, duration: 45 }
    ];

    for (const test of quickTests) {
      console.log(`🔍 ${test.name} (${test.users} usuários, ${test.duration}s)`);
      
      try {
        const result = await this.executeLoadTest(test);
        this.results.push({
          phase: 'Quick Load',
          test: test.name,
          status: 'SUCCESS',
          metrics: result,
          timestamp: new Date()
        });
        
        console.log(`✅ ${test.name}: OK`);
        console.log(`   - RPS: ${result.avgRPS}`);
        console.log(`   - Latência: ${result.avgLatency}ms`);
        console.log(`   - Erros: ${result.errorRate}%`);
        
      } catch (error) {
        console.log(`❌ ${test.name}: FALHOU - ${error.message}`);
        this.results.push({
          phase: 'Quick Load',
          test: test.name,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date()
        });
      }
      
      await this.sleep(10000); // Pausa de 10s entre testes
    }
  }

  async runQuickStressTest() {
    const quickStressTests = [
      { name: 'Stress CPU Leve', type: 'cpu', intensity: 'medium' },
      { name: 'Stress Conexões', type: 'connections', intensity: 'medium' }
    ];

    for (const test of quickStressTests) {
      console.log(`🔍 ${test.name}`);
      
      try {
        const result = await this.executeStressTest(test);
        this.results.push({
          phase: 'Quick Stress',
          test: test.name,
          status: 'SUCCESS',
          metrics: result,
          timestamp: new Date()
        });
        
        console.log(`✅ ${test.name}: Sistema resistiu`);
        
      } catch (error) {
        console.log(`❌ ${test.name}: Sistema falhou - ${error.message}`);
        this.results.push({
          phase: 'Quick Stress',
          test: test.name,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date()
        });
      }
      
      await this.sleep(15000); // Pausa de 15s para recuperação
    }
  }

  async runQuickFailureTest() {
    const quickFailureTests = [
      { name: 'Flood de Requests', type: 'request_flood' },
      { name: 'Dados Inválidos', type: 'data_corruption' }
    ];

    for (const test of quickFailureTests) {
      console.log(`🔍 ${test.name}`);
      
      try {
        const result = await this.executeFailureTest(test);
        this.results.push({
          phase: 'Quick Failure',
          test: test.name,
          status: 'SUCCESS',
          metrics: result,
          timestamp: new Date()
        });
        
        console.log(`✅ ${test.name}: Sistema se recuperou`);
        
      } catch (error) {
        console.log(`❌ ${test.name}: Falha crítica - ${error.message}`);
        this.results.push({
          phase: 'Quick Failure',
          test: test.name,
          status: 'CRITICAL',
          error: error.message,
          timestamp: new Date()
        });
      }
      
      await this.sleep(10000);
    }
  }

  async generateQuickReport() {
    const report = {
      testSuite: 'Quick Resilience Tests',
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.endTime - this.startTime,
      totalTests: this.results.length,
      passed: this.results.filter(r => r.status === 'SUCCESS').length,
      failed: this.results.filter(r => r.status === 'FAILED').length,
      critical: this.results.filter(r => r.status === 'CRITICAL').length,
      results: this.results
    };

    const reportFile = path.join(this.reportDir, `quick-resilience-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

    console.log('📊 RELATÓRIO RÁPIDO DE RESILIÊNCIA');
    console.log('==================================');
    console.log(`📁 Relatório: ${reportFile}`);
    console.log(`⏱️ Duração: ${Math.round(report.duration / 1000)}s`);
    console.log(`✅ Aprovados: ${report.passed}`);
    console.log(`❌ Falharam: ${report.failed}`);
    console.log(`🚨 Críticos: ${report.critical}`);
    console.log('');
    
    // Análise dos resultados
    if (report.critical > 0) {
      console.log('🚨 ATENÇÃO: Falhas críticas detectadas!');
      console.log('   O sistema precisa de correções antes dos testes completos.');
    } else if (report.failed > 0) {
      console.log('⚠️ AVISO: Algumas falhas detectadas.');
      console.log('   Revisar antes de prosseguir com testes completos.');
    } else {
      console.log('🎉 SUCESSO: Todos os testes rápidos passaram!');
      console.log('   Sistema pronto para testes de resiliência completos.');
    }
    
    console.log('');
    console.log('🔄 PRÓXIMOS PASSOS:');
    if (report.critical === 0 && report.failed === 0) {
      console.log('   1. Executar suíte completa: node test-suite-manager.cjs');
      console.log('   2. Monitorar métricas durante 24h');
      console.log('   3. Analisar relatórios detalhados');
    } else {
      console.log('   1. Corrigir problemas identificados');
      console.log('   2. Executar teste rápido novamente');
      console.log('   3. Só então prosseguir com testes completos');
    }
    
    return report;
  }
}

// Executar teste rápido se chamado diretamente
if (require.main === module) {
  const quickTest = new QuickResilienceTest();
  quickTest.runQuickTests().catch(console.error);
}

module.exports = QuickResilienceTest;
