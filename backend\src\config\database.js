/**
 * Configurações otimizadas do banco de dados para suportar 10+ usuários simultâneos
 */

import { PrismaClient } from '@prisma/client';

// Configurações otimizadas para connection pooling
const getDatabaseConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isTesting = process.env.TESTING === 'true';
  
  return {
    // Configurações de log otimizadas
    log: isProduction ? ['error'] : ['query', 'info', 'warn', 'error'],

    // Configurações de erro
    errorFormat: 'pretty',

    // Configurações de transação
    transactionOptions: {
      maxWait: 5000,
      timeout: 10000,
      isolationLevel: 'ReadCommitted'
    }
  };
};

// Instância otimizada do Prisma
let prisma;

const createPrismaClient = () => {
  if (prisma) {
    return prisma;
  }
  
  const config = getDatabaseConfig();
  
  prisma = new PrismaClient(config);
  
  // Middleware para logging de performance
  prisma.$use(async (params, next) => {
    const before = Date.now();
    const result = await next(params);
    const after = Date.now();
    
    const queryTime = after - before;
    
    // Log queries lentas (>500ms)
    if (queryTime > 500) {
      console.warn(`🐌 Query lenta detectada: ${params.model}.${params.action} - ${queryTime}ms`);
    }
    
    // Log queries muito lentas (>1000ms)
    if (queryTime > 1000) {
      console.error(`🚨 Query crítica: ${params.model}.${params.action} - ${queryTime}ms`);
    }
    
    return result;
  });
  
  // Middleware para retry automático
  prisma.$use(async (params, next) => {
    const maxRetries = 3;
    let retries = 0;
    
    while (retries < maxRetries) {
      try {
        return await next(params);
      } catch (error) {
        retries++;
        
        // Retry apenas para erros de conexão
        if (retries < maxRetries && isConnectionError(error)) {
          console.warn(`🔄 Retry ${retries}/${maxRetries} para ${params.model}.${params.action}`);
          await new Promise(resolve => setTimeout(resolve, 1000 * retries));
          continue;
        }
        
        throw error;
      }
    }
  });
  
  return prisma;
};

// Verificar se é erro de conexão
const isConnectionError = (error) => {
  const connectionErrors = [
    'ECONNREFUSED',
    'ENOTFOUND',
    'ETIMEDOUT',
    'ECONNRESET',
    'P1001', // Prisma connection error
    'P1008', // Operations timed out
    'P1017'  // Server has closed the connection
  ];
  
  return connectionErrors.some(code => 
    error.code === code || error.message?.includes(code)
  );
};

// Função para testar conexão
const testConnection = async () => {
  try {
    const client = createPrismaClient();
    await client.$queryRaw`SELECT 1`;
    console.log('✅ Conexão com banco de dados testada com sucesso');
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar conexão:', error.message);
    return false;
  }
};

// Função para obter estatísticas de conexão
const getConnectionStats = async () => {
  try {
    const client = createPrismaClient();
    
    // Para SQLite, estatísticas básicas
    const stats = {
      activeConnections: 1, // SQLite é single connection
      maxConnections: getDatabaseConfig().connectionLimit || 1,
      environment: process.env.NODE_ENV,
      testing: process.env.TESTING === 'true'
    };
    
    return stats;
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas:', error.message);
    return null;
  }
};

// Função para fechar conexões graciosamente
const closeConnections = async () => {
  try {
    if (prisma) {
      await prisma.$disconnect();
      console.log('✅ Conexões fechadas graciosamente');
    }
  } catch (error) {
    console.error('❌ Erro ao fechar conexões:', error.message);
  }
};

export {
  createPrismaClient,
  testConnection,
  getConnectionStats,
  closeConnections,
  getDatabaseConfig
};

export default createPrismaClient();
