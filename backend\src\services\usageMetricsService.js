/**
 * Serviço de Métricas de Uso para Ajuste Fino do Rate Limiting
 * Coleta dados reais de uso para calibrar limites adequadamente
 */

import redisService from './redisService.js';

class UsageMetricsService {
  constructor() {
    this.prefix = 'usage_metrics:';
    this.metricsBuffer = [];
    this.bufferSize = 100;
    this.flushInterval = 30000; // 30 segundos
    
    // Iniciar flush periódico
    this.startPeriodicFlush();
    
    console.log('📊 Usage Metrics Service inicializado');
  }

  /**
   * Registrar uso de endpoint por usuário
   */
  async recordUsage(userId, endpoint, method = 'GET', metadata = {}) {
    const usage = {
      userId: userId || 'anonymous',
      endpoint,
      method,
      timestamp: Date.now(),
      metadata
    };

    // Adicionar ao buffer
    this.metricsBuffer.push(usage);

    // Flush se buffer estiver cheio
    if (this.metricsBuffer.length >= this.bufferSize) {
      await this.flushMetrics();
    }

    // Registrar métricas em tempo real no Redis
    await this.recordRealTimeMetrics(usage);
  }

  /**
   * Registrar métricas em tempo real
   */
  async recordRealTimeMetrics(usage) {
    try {
      const client = await redisService.getClient();
      if (!client) return;

      const now = new Date();
      const hour = now.getHours();
      const day = now.getDate();
      const month = now.getMonth() + 1;

      // Chaves para diferentes períodos
      const keys = {
        hourly: `${this.prefix}hourly:${usage.userId}:${month}-${day}-${hour}`,
        daily: `${this.prefix}daily:${usage.userId}:${month}-${day}`,
        monthly: `${this.prefix}monthly:${usage.userId}:${month}`,
        endpoint: `${this.prefix}endpoint:${usage.endpoint}:${month}-${day}`,
        global: `${this.prefix}global:${month}-${day}-${hour}`
      };

      // Incrementar contadores
      const pipeline = client.multi();
      
      Object.values(keys).forEach(key => {
        pipeline.incr(key);
        pipeline.expire(key, 86400 * 31); // 31 dias
      });

      // Adicionar metadata específica
      if (usage.metadata.responseTime) {
        const latencyKey = `${this.prefix}latency:${usage.endpoint}:${month}-${day}`;
        pipeline.lpush(latencyKey, usage.metadata.responseTime);
        pipeline.ltrim(latencyKey, 0, 999); // Manter últimas 1000 medições
        pipeline.expire(latencyKey, 86400 * 7); // 7 dias
      }

      await pipeline.exec();

    } catch (error) {
      console.error('❌ Erro ao registrar métricas em tempo real:', error.message);
    }
  }

  /**
   * Flush periódico das métricas
   */
  startPeriodicFlush() {
    setInterval(async () => {
      if (this.metricsBuffer.length > 0) {
        await this.flushMetrics();
      }
    }, this.flushInterval);
  }

  /**
   * Flush das métricas do buffer
   */
  async flushMetrics() {
    if (this.metricsBuffer.length === 0) return;

    const metrics = [...this.metricsBuffer];
    this.metricsBuffer = [];

    try {
      // Processar métricas em lote
      await this.processBatchMetrics(metrics);
      console.log(`📊 ${metrics.length} métricas processadas`);
    } catch (error) {
      console.error('❌ Erro ao processar métricas:', error.message);
      // Recolocar no buffer em caso de erro
      this.metricsBuffer.unshift(...metrics);
    }
  }

  /**
   * Processar métricas em lote
   */
  async processBatchMetrics(metrics) {
    try {
      const client = await redisService.getClient();
      if (!client) return;

      const pipeline = client.multi();

      // Agrupar métricas por usuário e período
      const grouped = this.groupMetrics(metrics);

      // Processar grupos
      for (const [key, data] of Object.entries(grouped)) {
        const batchKey = `${this.prefix}batch:${key}`;
        pipeline.incrBy(batchKey, data.count);
        pipeline.expire(batchKey, 86400 * 31); // 31 dias

        // Adicionar detalhes se necessário
        if (data.details.length > 0) {
          const detailsKey = `${this.prefix}details:${key}`;
          pipeline.lpush(detailsKey, ...data.details.map(d => JSON.stringify(d)));
          pipeline.ltrim(detailsKey, 0, 999);
          pipeline.expire(detailsKey, 86400 * 7); // 7 dias
        }
      }

      await pipeline.exec();

    } catch (error) {
      console.error('❌ Erro ao processar lote de métricas:', error.message);
      throw error;
    }
  }

  /**
   * Agrupar métricas por chaves relevantes
   */
  groupMetrics(metrics) {
    const grouped = {};

    metrics.forEach(metric => {
      const now = new Date(metric.timestamp);
      const hour = now.getHours();
      const day = now.getDate();
      const month = now.getMonth() + 1;

      const keys = [
        `${metric.userId}:${month}-${day}-${hour}`,
        `${metric.endpoint}:${month}-${day}`,
        `global:${month}-${day}-${hour}`
      ];

      keys.forEach(key => {
        if (!grouped[key]) {
          grouped[key] = { count: 0, details: [] };
        }
        grouped[key].count++;
        grouped[key].details.push({
          endpoint: metric.endpoint,
          method: metric.method,
          timestamp: metric.timestamp,
          metadata: metric.metadata
        });
      });
    });

    return grouped;
  }

  /**
   * Obter métricas de uso por usuário
   */
  async getUserUsageMetrics(userId, period = 'daily') {
    try {
      const client = await redisService.getClient();
      if (!client) return null;

      const now = new Date();
      const patterns = this.getPeriodPatterns(userId, period, now);
      
      const results = {};
      
      for (const [key, pattern] of Object.entries(patterns)) {
        const keys = await client.keys(pattern);
        const values = keys.length > 0 ? await client.mget(...keys) : [];
        
        results[key] = values.reduce((sum, val) => sum + (parseInt(val) || 0), 0);
      }

      return results;

    } catch (error) {
      console.error('❌ Erro ao obter métricas do usuário:', error.message);
      return null;
    }
  }

  /**
   * Obter padrões de período
   */
  getPeriodPatterns(userId, period, now) {
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const hour = now.getHours();

    switch (period) {
      case 'hourly':
        return {
          current: `${this.prefix}hourly:${userId}:${month}-${day}-${hour}`,
          last: `${this.prefix}hourly:${userId}:${month}-${day}-${hour - 1}`
        };
      
      case 'daily':
        return {
          current: `${this.prefix}daily:${userId}:${month}-${day}`,
          last: `${this.prefix}daily:${userId}:${month}-${day - 1}`
        };
      
      case 'monthly':
        return {
          current: `${this.prefix}monthly:${userId}:${month}`,
          last: `${this.prefix}monthly:${userId}:${month - 1}`
        };
      
      default:
        return {
          current: `${this.prefix}daily:${userId}:${month}-${day}`
        };
    }
  }

  /**
   * Analisar padrões de uso para ajuste de rate limiting
   */
  async analyzeUsagePatterns(userId = null) {
    try {
      const client = await redisService.getClient();
      if (!client) return null;

      const now = new Date();
      const analysis = {
        timestamp: now.toISOString(),
        userId,
        patterns: {},
        recommendations: {}
      };

      // Analisar últimos 7 dias
      const days = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date(now - i * 24 * 60 * 60 * 1000);
        days.push({
          month: date.getMonth() + 1,
          day: date.getDate()
        });
      }

      // Coletar dados
      const dailyUsage = [];
      const hourlyPeaks = [];

      for (const { month, day } of days) {
        const pattern = userId ? 
          `${this.prefix}daily:${userId}:${month}-${day}` :
          `${this.prefix}global:${month}-${day}-*`;

        if (userId) {
          const usage = await client.get(pattern);
          dailyUsage.push(parseInt(usage) || 0);
        } else {
          const keys = await client.keys(pattern);
          const values = keys.length > 0 ? await client.mget(...keys) : [];
          const total = values.reduce((sum, val) => sum + (parseInt(val) || 0), 0);
          dailyUsage.push(total);
        }

        // Analisar picos horários
        for (let hour = 0; hour < 24; hour++) {
          const hourPattern = userId ?
            `${this.prefix}hourly:${userId}:${month}-${day}-${hour}` :
            `${this.prefix}global:${month}-${day}-${hour}`;

          const hourUsage = userId ?
            await client.get(hourPattern) :
            (await client.keys(hourPattern)).length;

          if (hourUsage > 0) {
            hourlyPeaks.push({ hour, usage: parseInt(hourUsage) || 0 });
          }
        }
      }

      // Calcular estatísticas
      analysis.patterns = {
        avgDailyUsage: dailyUsage.reduce((a, b) => a + b, 0) / dailyUsage.length,
        maxDailyUsage: Math.max(...dailyUsage),
        minDailyUsage: Math.min(...dailyUsage),
        peakHours: this.findPeakHours(hourlyPeaks),
        trend: this.calculateTrend(dailyUsage)
      };

      // Gerar recomendações
      analysis.recommendations = this.generateRateLimitRecommendations(analysis.patterns);

      return analysis;

    } catch (error) {
      console.error('❌ Erro ao analisar padrões de uso:', error.message);
      return null;
    }
  }

  /**
   * Encontrar horários de pico
   */
  findPeakHours(hourlyData) {
    const hourlyTotals = {};
    
    hourlyData.forEach(({ hour, usage }) => {
      hourlyTotals[hour] = (hourlyTotals[hour] || 0) + usage;
    });

    return Object.entries(hourlyTotals)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([hour, usage]) => ({ hour: parseInt(hour), usage }));
  }

  /**
   * Calcular tendência
   */
  calculateTrend(dailyUsage) {
    if (dailyUsage.length < 2) return 'stable';
    
    const recent = dailyUsage.slice(0, 3).reduce((a, b) => a + b, 0) / 3;
    const older = dailyUsage.slice(-3).reduce((a, b) => a + b, 0) / 3;
    
    const change = (recent - older) / older * 100;
    
    if (change > 20) return 'increasing';
    if (change < -20) return 'decreasing';
    return 'stable';
  }

  /**
   * Gerar recomendações de rate limiting
   */
  generateRateLimitRecommendations(patterns) {
    const recommendations = {};

    // Recomendação baseada no uso médio
    const avgUsage = patterns.avgDailyUsage;
    const maxUsage = patterns.maxDailyUsage;

    // Calcular limites recomendados (com margem de segurança)
    const dailyLimit = Math.ceil(maxUsage * 1.5); // 50% de margem
    const hourlyLimit = Math.ceil(dailyLimit / 24 * 2); // Assumindo distribuição não uniforme
    const burstLimit = Math.ceil(hourlyLimit * 0.3); // 30% do limite horário

    recommendations.limits = {
      daily: dailyLimit,
      hourly: hourlyLimit,
      burst: burstLimit,
      confidence: this.calculateConfidence(patterns)
    };

    // Recomendações específicas
    recommendations.suggestions = [];

    if (patterns.trend === 'increasing') {
      recommendations.suggestions.push('Uso crescente detectado - considere aumentar limites gradualmente');
    }

    if (patterns.peakHours.length > 0) {
      const topPeak = patterns.peakHours[0];
      recommendations.suggestions.push(`Pico de uso às ${topPeak.hour}h - considere limites flexíveis por horário`);
    }

    if (maxUsage > avgUsage * 3) {
      recommendations.suggestions.push('Grande variação no uso - implemente burst limits mais generosos');
    }

    return recommendations;
  }

  /**
   * Calcular confiança da recomendação
   */
  calculateConfidence(patterns) {
    let confidence = 0.5; // Base

    // Mais dados = mais confiança
    if (patterns.avgDailyUsage > 10) confidence += 0.2;
    if (patterns.avgDailyUsage > 50) confidence += 0.2;

    // Tendência estável = mais confiança
    if (patterns.trend === 'stable') confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Obter estatísticas globais
   */
  async getGlobalStats() {
    try {
      const client = await redisService.getClient();
      if (!client) return null;

      const now = new Date();
      const today = `${now.getMonth() + 1}-${now.getDate()}`;
      
      const keys = await client.keys(`${this.prefix}*:${today}*`);
      const values = keys.length > 0 ? await client.mget(...keys) : [];
      
      const totalRequests = values.reduce((sum, val) => sum + (parseInt(val) || 0), 0);
      const uniqueUsers = new Set(
        keys.filter(k => k.includes('daily:'))
             .map(k => k.split(':')[2])
      ).size;

      return {
        totalRequests,
        uniqueUsers,
        avgRequestsPerUser: uniqueUsers > 0 ? Math.round(totalRequests / uniqueUsers) : 0,
        timestamp: now.toISOString()
      };

    } catch (error) {
      console.error('❌ Erro ao obter estatísticas globais:', error.message);
      return null;
    }
  }
}

export default new UsageMetricsService();
