/**
 * Teste simples do cache de resposta
 */

import http from 'http';

function makeRequest(path) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      console.error('Erro na requisição:', err);
      resolve(null);
    });

    req.end();
  });
}

async function testCache() {
  console.log('🧪 TESTE SIMPLES DE CACHE');
  console.log('========================');

  // Teste 1: Rota principal
  console.log('\n1️⃣ Testando rota principal (/)...');
  
  const first = await makeRequest('/');
  console.log(`   Primeira requisição: ${first?.status}`);
  console.log(`   Cache Header: ${first?.headers['x-cache'] || 'N/A'}`);
  console.log(`   Cache Key: ${first?.headers['x-cache-key'] || 'N/A'}`);

  // Aguardar um pouco
  await new Promise(resolve => setTimeout(resolve, 500));

  const second = await makeRequest('/');
  console.log(`   Segunda requisição: ${second?.status}`);
  console.log(`   Cache Header: ${second?.headers['x-cache'] || 'N/A'}`);
  console.log(`   Cache Key: ${second?.headers['x-cache-key'] || 'N/A'}`);

  // Teste 2: CSRF Token
  console.log('\n2️⃣ Testando CSRF Token...');
  
  const csrf1 = await makeRequest('/csrf-token');
  console.log(`   Primeira requisição: ${csrf1?.status}`);
  console.log(`   Cache Header: ${csrf1?.headers['x-cache'] || 'N/A'}`);

  await new Promise(resolve => setTimeout(resolve, 500));

  const csrf2 = await makeRequest('/csrf-token');
  console.log(`   Segunda requisição: ${csrf2?.status}`);
  console.log(`   Cache Header: ${csrf2?.headers['x-cache'] || 'N/A'}`);

  // Teste 3: Health
  console.log('\n3️⃣ Testando Health...');
  
  const health1 = await makeRequest('/health');
  console.log(`   Primeira requisição: ${health1?.status}`);
  console.log(`   Cache Header: ${health1?.headers['x-cache'] || 'N/A'}`);

  await new Promise(resolve => setTimeout(resolve, 500));

  const health2 = await makeRequest('/health');
  console.log(`   Segunda requisição: ${health2?.status}`);
  console.log(`   Cache Header: ${health2?.headers['x-cache'] || 'N/A'}`);

  console.log('\n✅ Teste concluído!');
}

testCache().catch(console.error);
