/**
 * Teste de Carga Gradual
 * Aumenta a carga progressivamente para encontrar limites do sistema
 */

const http = require('http');

class GradualLoadTest {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.results = [];
  }

  async runGradualTest() {
    console.log('📈 TESTE DE CARGA GRADUAL');
    console.log('=========================');
    console.log('');

    // Fases de carga crescente
    const phases = [
      { name: 'Fase 1', concurrent: 2, duration: 30, description: '2 usuários por 30s' },
      { name: 'Fase 2', concurrent: 5, duration: 30, description: '5 usuários por 30s' },
      { name: 'Fase 3', concurrent: 10, duration: 45, description: '10 usuários por 45s' },
      { name: 'Fase 4', concurrent: 20, duration: 45, description: '20 usuários por 45s' },
      { name: 'Fase 5', concurrent: 30, duration: 60, description: '30 usuários por 60s' }
    ];

    for (const phase of phases) {
      console.log(`🔄 ${phase.name}: ${phase.description}`);
      
      try {
        const result = await this.executePhase(phase);
        this.results.push(result);
        
        this.printPhaseResult(result);
        
        // Se a fase falhou criticamente, parar o teste
        if (result.successRate < 50) {
          console.log('🚨 Taxa de sucesso muito baixa - parando teste gradual');
          break;
        }
        
        // Pausa entre fases para recuperação
        console.log('⏳ Aguardando recuperação do sistema...');
        await this.sleep(15000);
        console.log('');
        
      } catch (error) {
        console.log(`❌ ${phase.name}: Erro crítico - ${error.message}`);
        this.results.push({
          phase: phase.name,
          success: false,
          error: error.message
        });
        break;
      }
    }

    this.generateReport();
  }

  async executePhase(phase) {
    const startTime = Date.now();
    const endTime = startTime + (phase.duration * 1000);
    
    let totalRequests = 0;
    let successfulRequests = 0;
    let failedRequests = 0;
    const responseTimes = [];
    const errors = [];

    // Criar usuários virtuais
    const users = [];
    for (let i = 0; i < phase.concurrent; i++) {
      users.push({
        id: i + 1,
        email: `loadtest${(i % 50) + 1}@code2post.com`,
        password: 'LoadTest123!',
        active: true
      });
    }

    // Executar usuários concorrentemente
    const userPromises = users.map(user => this.simulateUser(user, endTime, responseTimes, errors));
    
    // Aguardar conclusão de todos os usuários
    const userResults = await Promise.allSettled(userPromises);
    
    // Contar resultados
    userResults.forEach(result => {
      if (result.status === 'fulfilled') {
        totalRequests += result.value.requests;
        successfulRequests += result.value.successful;
        failedRequests += result.value.failed;
      }
    });

    const actualDuration = Date.now() - startTime;
    const successRate = totalRequests > 0 ? Math.round((successfulRequests / totalRequests) * 100) : 0;
    const avgResponseTime = responseTimes.length > 0 
      ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
      : 0;
    const rps = Math.round((totalRequests / (actualDuration / 1000)) * 100) / 100;

    return {
      phase: phase.name,
      concurrent: phase.concurrent,
      duration: Math.round(actualDuration / 1000),
      totalRequests,
      successfulRequests,
      failedRequests,
      successRate,
      avgResponseTime,
      rps,
      errors: errors.slice(0, 5), // Primeiros 5 erros
      success: successRate >= 80 // Considerar sucesso se >= 80%
    };
  }

  async simulateUser(user, endTime, responseTimes, errors) {
    let requests = 0;
    let successful = 0;
    let failed = 0;

    while (Date.now() < endTime && user.active) {
      try {
        const startTime = Date.now();
        const response = await this.makeRequest('/auth/login', 'POST', {
          email: user.email,
          password: user.password
        });
        
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
        
        requests++;
        
        if (response.status === 200) {
          successful++;
        } else {
          failed++;
          if (errors.length < 10) { // Limitar erros coletados
            errors.push(`User ${user.id}: HTTP ${response.status}`);
          }
        }
        
        // Pausa entre requests (1-3 segundos)
        await this.sleep(1000 + Math.random() * 2000);
        
      } catch (error) {
        requests++;
        failed++;
        
        if (errors.length < 10) {
          errors.push(`User ${user.id}: ${error.message}`);
        }
        
        // Se muitos erros consecutivos, desativar usuário
        if (failed > successful + 5) {
          user.active = false;
        }
        
        await this.sleep(2000); // Pausa maior em caso de erro
      }
    }

    return { requests, successful, failed };
  }

  async makeRequest(path, method = 'POST', data = null) {
    return new Promise((resolve, reject) => {
      const postData = data ? JSON.stringify(data) : '';
      
      const options = {
        hostname: 'localhost',
        port: 3001,
        path,
        method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'GradualLoadTest'
        },
        timeout: 8000 // 8 segundos timeout
      };

      if (data) {
        options.headers['Content-Length'] = Buffer.byteLength(postData);
      }

      const req = http.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => { responseData += chunk; });
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (data) {
        req.write(postData);
      }
      
      req.end();
    });
  }

  printPhaseResult(result) {
    const icon = result.success ? '✅' : '❌';
    console.log(`   ${icon} ${result.phase}: ${result.successRate}% sucesso`);
    console.log(`      Requests: ${result.totalRequests} (${result.rps} RPS)`);
    console.log(`      Tempo médio: ${result.avgResponseTime}ms`);
    console.log(`      Sucessos: ${result.successfulRequests} | Falhas: ${result.failedRequests}`);
    
    if (result.errors.length > 0) {
      console.log(`      Erros: ${result.errors.slice(0, 2).join(', ')}`);
    }
  }

  generateReport() {
    console.log('');
    console.log('📊 RELATÓRIO DO TESTE DE CARGA GRADUAL');
    console.log('======================================');
    
    const successfulPhases = this.results.filter(r => r.success).length;
    const totalPhases = this.results.length;
    
    console.log(`✅ Fases aprovadas: ${successfulPhases}/${totalPhases}`);
    console.log('');
    
    // Encontrar limite do sistema
    const lastSuccessfulPhase = this.results.filter(r => r.success).pop();
    const firstFailedPhase = this.results.find(r => !r.success);
    
    if (lastSuccessfulPhase) {
      console.log('🎯 CAPACIDADE MÁXIMA IDENTIFICADA:');
      console.log(`   Usuários simultâneos: ${lastSuccessfulPhase.concurrent}`);
      console.log(`   RPS sustentado: ${lastSuccessfulPhase.rps}`);
      console.log(`   Taxa de sucesso: ${lastSuccessfulPhase.successRate}%`);
      console.log(`   Tempo de resposta: ${lastSuccessfulPhase.avgResponseTime}ms`);
    }
    
    if (firstFailedPhase) {
      console.log('');
      console.log('⚠️ LIMITE ENCONTRADO:');
      console.log(`   Falha em: ${firstFailedPhase.concurrent} usuários simultâneos`);
      console.log(`   Taxa de sucesso: ${firstFailedPhase.successRate}%`);
    }
    
    console.log('');
    console.log('🔄 RECOMENDAÇÕES:');
    if (successfulPhases === totalPhases) {
      console.log('   ✅ Sistema suportou todas as fases testadas');
      console.log('   📈 Pode testar cargas ainda maiores');
    } else {
      console.log('   🔧 Otimizar sistema para suportar mais usuários simultâneos');
      console.log('   📊 Considerar rate limiting mais inteligente');
      console.log('   🔄 Melhorar recuperação após sobrecarga');
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const test = new GradualLoadTest();
  test.runGradualTest().catch(console.error);
}

module.exports = GradualLoadTest;
