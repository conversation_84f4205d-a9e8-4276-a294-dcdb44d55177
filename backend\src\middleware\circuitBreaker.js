/**
 * Circuit Breaker Middleware para melhorar recuperação após sobrecarga
 * Implementa padrão Circuit Breaker para isolar falhas e evitar cascateamento
 */

class CircuitBreaker {
  constructor(options = {}) {
    this.name = options.name || 'default';
    this.failureThreshold = options.failureThreshold || 5;
    this.recoveryTimeout = options.recoveryTimeout || 60000; // 1 minuto
    this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 segundos
    this.expectedErrors = options.expectedErrors || [];
    
    // Estados: CLOSED, OPEN, HALF_OPEN
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.nextAttempt = null;
    
    // Estatísticas
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      circuitOpenCount: 0,
      lastReset: new Date()
    };
    
    console.log(`🔧 Circuit Breaker '${this.name}' inicializado`);
  }

  /**
   * Executar função com circuit breaker
   */
  async execute(fn, ...args) {
    this.stats.totalRequests++;
    
    // Verificar estado do circuit
    if (this.state === 'OPEN') {
      if (this.canAttemptReset()) {
        this.state = 'HALF_OPEN';
        console.log(`🔄 Circuit Breaker '${this.name}': Tentando recuperação (HALF_OPEN)`);
      } else {
        this.stats.circuitOpenCount++;
        throw new Error(`Circuit Breaker '${this.name}' está OPEN. Próxima tentativa em ${this.getTimeToNextAttempt()}ms`);
      }
    }

    try {
      const result = await fn(...args);
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  /**
   * Callback de sucesso
   */
  onSuccess() {
    this.stats.successfulRequests++;
    
    if (this.state === 'HALF_OPEN') {
      this.reset();
      console.log(`✅ Circuit Breaker '${this.name}': Recuperação bem-sucedida (CLOSED)`);
    }
    
    // Reset contador de falhas em estado CLOSED
    if (this.state === 'CLOSED') {
      this.failureCount = 0;
    }
  }

  /**
   * Callback de falha
   */
  onFailure(error) {
    this.stats.failedRequests++;
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    // Verificar se é erro esperado (não conta para circuit breaker)
    if (this.isExpectedError(error)) {
      console.log(`⚠️ Circuit Breaker '${this.name}': Erro esperado ignorado - ${error.message}`);
      return;
    }
    
    console.log(`❌ Circuit Breaker '${this.name}': Falha ${this.failureCount}/${this.failureThreshold} - ${error.message}`);
    
    // Abrir circuit se atingiu threshold
    if (this.failureCount >= this.failureThreshold) {
      this.open();
    }
  }

  /**
   * Abrir circuit breaker
   */
  open() {
    this.state = 'OPEN';
    this.nextAttempt = Date.now() + this.recoveryTimeout;
    console.log(`🚨 Circuit Breaker '${this.name}': ABERTO por ${this.recoveryTimeout}ms`);
  }

  /**
   * Resetar circuit breaker
   */
  reset() {
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.nextAttempt = null;
    this.stats.lastReset = new Date();
  }

  /**
   * Verificar se pode tentar reset
   */
  canAttemptReset() {
    return Date.now() >= this.nextAttempt;
  }

  /**
   * Tempo até próxima tentativa
   */
  getTimeToNextAttempt() {
    return Math.max(0, this.nextAttempt - Date.now());
  }

  /**
   * Verificar se é erro esperado
   */
  isExpectedError(error) {
    return this.expectedErrors.some(expectedError => {
      if (typeof expectedError === 'string') {
        return error.message.includes(expectedError);
      }
      if (expectedError instanceof RegExp) {
        return expectedError.test(error.message);
      }
      if (typeof expectedError === 'function') {
        return expectedError(error);
      }
      return false;
    });
  }

  /**
   * Obter estatísticas
   */
  getStats() {
    const now = new Date();
    const uptime = now - this.stats.lastReset;
    
    return {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      failureThreshold: this.failureThreshold,
      ...this.stats,
      successRate: this.stats.totalRequests > 0 ? 
        (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%',
      uptime: Math.round(uptime / 1000) + 's',
      nextAttempt: this.nextAttempt ? new Date(this.nextAttempt).toISOString() : null
    };
  }

  /**
   * Middleware Express
   */
  middleware() {
    return async (req, res, next) => {
      try {
        await this.execute(async () => {
          return new Promise((resolve, reject) => {
            const originalSend = res.send;
            const originalJson = res.json;
            
            // Interceptar resposta para detectar erros
            res.send = function(data) {
              if (res.statusCode >= 500) {
                reject(new Error(`HTTP ${res.statusCode}: ${data}`));
              } else {
                resolve(data);
              }
              return originalSend.call(this, data);
            };
            
            res.json = function(data) {
              if (res.statusCode >= 500) {
                reject(new Error(`HTTP ${res.statusCode}: ${JSON.stringify(data)}`));
              } else {
                resolve(data);
              }
              return originalJson.call(this, data);
            };
            
            next();
          });
        });
      } catch (error) {
        res.status(503).json({
          error: 'Service Temporarily Unavailable',
          message: `Circuit Breaker '${this.name}' is open`,
          retryAfter: Math.ceil(this.getTimeToNextAttempt() / 1000)
        });
      }
    };
  }
}

// Circuit Breakers pré-configurados
const circuitBreakers = {
  database: new CircuitBreaker({
    name: 'database',
    failureThreshold: 3,
    recoveryTimeout: 30000, // 30 segundos
    expectedErrors: ['SQLITE_BUSY', 'SQLITE_LOCKED']
  }),
  
  redis: new CircuitBreaker({
    name: 'redis',
    failureThreshold: 5,
    recoveryTimeout: 15000, // 15 segundos
    expectedErrors: ['ECONNREFUSED', 'Redis connection']
  }),
  
  auth: new CircuitBreaker({
    name: 'auth',
    failureThreshold: 10,
    recoveryTimeout: 60000, // 1 minuto
    expectedErrors: ['Invalid credentials', 'User not found']
  }),
  
  github: new CircuitBreaker({
    name: 'github',
    failureThreshold: 5,
    recoveryTimeout: 120000, // 2 minutos
    expectedErrors: ['rate limit', 'API rate limit']
  }),
  
  gemini: new CircuitBreaker({
    name: 'gemini',
    failureThreshold: 3,
    recoveryTimeout: 180000, // 3 minutos
    expectedErrors: ['quota exceeded', 'API quota']
  })
};

/**
 * Obter estatísticas de todos os circuit breakers
 */
const getAllStats = () => {
  return Object.values(circuitBreakers).map(cb => cb.getStats());
};

/**
 * Resetar todos os circuit breakers
 */
const resetAll = () => {
  Object.values(circuitBreakers).forEach(cb => cb.reset());
  console.log('🔄 Todos os Circuit Breakers foram resetados');
};

/**
 * Middleware para monitoramento de circuit breakers
 */
const monitoringMiddleware = (req, res, next) => {
  if (req.path === '/health/circuit-breakers') {
    return res.json({
      circuitBreakers: getAllStats(),
      timestamp: new Date().toISOString()
    });
  }
  next();
};

export {
  CircuitBreaker,
  circuitBreakers,
  getAllStats,
  resetAll,
  monitoringMiddleware
};

export default circuitBreakers;
