# Runbook Operacional - CODE2POST
**Versão:** 2.0  
**Data:** 05 de Agosto de 2025  
**Responsável:** Time de Engenheiros  
**Última Atualização:** Implementação de otimizações de resiliência  

---

## 📋 Índice

1. [Visão Geral](#visão-geral)
2. [Monitoramento Diário](#monitoramento-diário)
3. [Resposta a Alertas](#resposta-a-alertas)
4. [Procedimentos de Manutenção](#procedimentos-de-manutenção)
5. [Troubleshooting](#troubleshooting)
6. [Disaster Recovery](#disaster-recovery)
7. [Escalation](#escalation)
8. [Contatos de Emergência](#contatos-de-emergência)

---

## 🎯 Visão Geral

### Sistema
- **Nome:** Code2Post API
- **Versão:** 1.6.0+
- **Ambiente:** Produção
- **URL:** https://api.code2post.com
- **Porta:** 3001
- **Banco:** PostgreSQL (Produção) / SQLite (Dev)
- **Cache:** Redis
- **Monitoramento:** Integrado com alertas automáticos

### SLAs Definidos
- **Uptime:** 99.9% (8.76 horas de downtime/ano)
- **Latência:** < 600ms (P95)
- **Taxa de Erro:** < 1%
- **Disponibilidade:** 99.5%
- **Tempo de Recuperação:** < 5 minutos

### Capacidade Atual
- **Usuários Simultâneos:** 30+ (testado)
- **RPS Sustentado:** 3.46
- **Taxa de Sucesso:** 92% com 30 usuários

---

## 📊 Monitoramento Diário

### 1. Health Checks Matinais (9:00 AM)

#### Verificações Básicas
```bash
# 1. Status do servidor
curl -s http://localhost:3001/health | jq '.'

# 2. Circuit breakers
curl -s http://localhost:3001/health/circuit-breakers | jq '.'

# 3. Métricas do sistema
curl -s http://localhost:3001/health/metrics | jq '.'

# 4. Cache Redis
redis-cli ping
redis-cli info memory
```

#### Checklist Diário
- [ ] Servidor respondendo (HTTP 200)
- [ ] Todos os circuit breakers em estado CLOSED
- [ ] CPU < 80%
- [ ] Memória < 85%
- [ ] Redis conectado
- [ ] Banco de dados acessível
- [ ] Logs sem erros críticos
- [ ] Backups do dia anterior existem

### 2. Verificação de Logs

#### Localização dos Logs
```bash
# Logs da aplicação
tail -f logs/app.log

# Logs de erro
tail -f logs/error.log

# Logs de acesso
tail -f logs/access.log

# Logs do sistema
journalctl -u code2post -f
```

#### Padrões a Observar
- **🔍 Buscar por:** `ERROR`, `CRITICAL`, `TIMEOUT`, `FAILED`
- **⚠️ Alertas:** Taxa de erro > 5%
- **🐌 Performance:** Queries > 500ms
- **🔄 Circuit Breakers:** Estados OPEN ou HALF_OPEN

### 3. Métricas de Performance

#### Comandos de Monitoramento
```bash
# CPU e Memória
htop

# Processos Node.js
ps aux | grep node

# Conexões de rede
netstat -tulpn | grep :3001

# Espaço em disco
df -h

# Load average
uptime
```

#### Thresholds de Alerta
| Métrica | Warning | Critical |
|---------|---------|----------|
| CPU | 70% | 85% |
| Memória | 75% | 90% |
| Disco | 80% | 95% |
| Load Average | 2.0 | 4.0 |
| Latência | 800ms | 1500ms |

---

## 🚨 Resposta a Alertas

### Alertas Críticos (Resposta Imediata)

#### 1. Servidor Não Responde
**Sintomas:** HTTP 500/503, timeout de conexão
```bash
# Verificar processo
ps aux | grep node

# Verificar porta
netstat -tulpn | grep :3001

# Verificar logs
tail -20 logs/error.log

# Reiniciar se necessário
pm2 restart code2post
```

#### 2. Circuit Breaker OPEN
**Sintomas:** Alerta "Circuit Breaker 'X' está OPEN"
```bash
# Verificar status
curl http://localhost:3001/health/circuit-breakers

# Identificar causa raiz
# - Database: Verificar conexão PostgreSQL
# - Redis: Verificar conexão Redis
# - Auth: Verificar JWT e sessões
# - GitHub: Verificar API limits
# - Gemini: Verificar quota da API

# Ações corretivas específicas por tipo
```

#### 3. Taxa de Erro Alta (>5%)
**Sintomas:** Alerta "High Error Rate Detected"
```bash
# Analisar logs de erro
grep -i error logs/app.log | tail -50

# Verificar endpoints com mais erros
grep "HTTP 5" logs/access.log | awk '{print $7}' | sort | uniq -c | sort -nr

# Verificar circuit breakers
curl http://localhost:3001/health/circuit-breakers

# Verificar recursos do sistema
htop
```

#### 4. Latência Alta (>800ms)
**Sintomas:** Alerta "High Response Time"
```bash
# Verificar queries lentas
grep "Query lenta" logs/app.log | tail -20

# Verificar cache Redis
redis-cli info stats

# Verificar conexões do banco
# PostgreSQL: SELECT * FROM pg_stat_activity;
# SQLite: .timeout

# Verificar load do sistema
uptime
iostat -x 1 5
```

### Alertas de Warning (Resposta em 30min)

#### 1. CPU/Memória Alta
```bash
# Identificar processos
top -o %CPU
top -o %MEM

# Verificar memory leaks
node --inspect app.js
# Usar Chrome DevTools para análise

# Otimizar se necessário
pm2 reload code2post
```

#### 2. Espaço em Disco Baixo
```bash
# Verificar uso
du -sh * | sort -hr

# Limpar logs antigos
find logs/ -name "*.log" -mtime +30 -delete

# Limpar backups antigos
find backups/ -mtime +7 -delete

# Rotacionar logs
logrotate /etc/logrotate.d/code2post
```

---

## 🔧 Procedimentos de Manutenção

### Manutenção Semanal (Domingos, 2:00 AM)

#### 1. Backup Completo
```bash
# Executar backup
./scripts/backup-pre-deploy.sh

# Verificar integridade
ls -la backups/
```

#### 2. Limpeza de Logs
```bash
# Rotacionar logs
logrotate -f /etc/logrotate.d/code2post

# Limpar logs antigos
find logs/ -name "*.log.*" -mtime +7 -delete
```

#### 3. Atualização de Dependências
```bash
# Verificar vulnerabilidades
npm audit

# Atualizar dependências críticas
npm update

# Testar após atualizações
npm test
```

#### 4. Otimização do Banco
```bash
# PostgreSQL
psql -d code2post -c "VACUUM ANALYZE;"
psql -d code2post -c "REINDEX DATABASE code2post;"

# SQLite
sqlite3 dev.db "VACUUM;"
sqlite3 dev.db "ANALYZE;"
```

### Manutenção Mensal (Primeiro domingo do mês)

#### 1. Análise de Performance
```bash
# Executar testes de carga
node tests/resilience/gradual-load-test.cjs

# Analisar métricas
node scripts/analyze-performance.js

# Gerar relatório
node scripts/generate-monthly-report.js
```

#### 2. Revisão de Configurações
- [ ] Verificar limites de rate limiting
- [ ] Revisar configurações de circuit breaker
- [ ] Atualizar certificados SSL (se aplicável)
- [ ] Verificar configurações de monitoramento

#### 3. Teste de Disaster Recovery
```bash
# Executar teste completo
node tests/disaster-recovery/full-disaster-test.cjs

# Validar procedimentos de backup
./scripts/validate-backup.sh

# Testar rollback
./scripts/test-rollback.sh
```

---

## 🔍 Troubleshooting

### Problemas Comuns

#### 1. "Cannot connect to database"
**Causa:** Falha na conexão com PostgreSQL/SQLite
```bash
# PostgreSQL
pg_isready -h localhost -p 5432
systemctl status postgresql

# SQLite
ls -la prisma/dev.db
sqlite3 dev.db ".tables"

# Verificar configurações
cat .env | grep DATABASE_URL
```

#### 2. "Redis connection failed"
**Causa:** Redis indisponível
```bash
# Verificar Redis
redis-cli ping
systemctl status redis

# Verificar configurações
cat .env | grep REDIS_URL

# Reiniciar se necessário
systemctl restart redis
```

#### 3. "Rate limit exceeded"
**Causa:** Usuário atingiu limite de requests
```bash
# Verificar configurações
cat .env | grep RATE_LIMIT

# Verificar métricas do usuário
redis-cli keys "rate_limit:*"

# Ajustar limites se necessário
# Editar src/middleware/userRateLimit.js
```

#### 4. "JWT token invalid"
**Causa:** Problemas com autenticação
```bash
# Verificar secret
cat .env | grep JWT_SECRET

# Verificar logs de auth
grep "JWT" logs/app.log | tail -20

# Limpar cache de sessões se necessário
redis-cli flushdb
```

#### 5. "Circuit breaker is OPEN"
**Causa:** Muitas falhas consecutivas
```bash
# Identificar circuit breaker
curl http://localhost:3001/health/circuit-breakers

# Verificar causa raiz do componente
# Database: Verificar conexão
# Redis: Verificar disponibilidade
# Auth: Verificar JWT
# GitHub: Verificar API limits
# Gemini: Verificar quota

# Reset manual se necessário (cuidado!)
curl -X POST http://localhost:3001/admin/circuit-breaker/reset/[NAME]
```

### Comandos de Diagnóstico

#### Sistema
```bash
# Status geral
systemctl status code2post

# Processos
ps aux | grep node

# Memória
free -h
cat /proc/meminfo

# CPU
cat /proc/loadavg
vmstat 1 5

# Rede
netstat -tulpn | grep :3001
ss -tulpn | grep :3001

# Disco
df -h
iostat -x 1 5
```

#### Aplicação
```bash
# Logs em tempo real
tail -f logs/app.log

# Métricas
curl http://localhost:3001/health/metrics

# Cache
redis-cli info
redis-cli monitor

# Banco de dados
# PostgreSQL: \l \dt \d+ users
# SQLite: .tables .schema users
```

---

## 🚨 Disaster Recovery

### Cenários de Falha

#### 1. Falha Total do Servidor
**RTO:** 5 minutos | **RPO:** 1 hora
```bash
# 1. Verificar backup mais recente
ls -la backups/ | head -5

# 2. Executar rollback completo
./scripts/rollback.sh [TIMESTAMP]

# 3. Validar recuperação
./scripts/validate-rollback.sh

# 4. Notificar stakeholders
```

#### 2. Corrupção do Banco de Dados
**RTO:** 10 minutos | **RPO:** 4 horas
```bash
# 1. Parar aplicação
pm2 stop code2post

# 2. Restaurar backup do banco
cp backups/[LATEST]/database.backup prisma/dev.db

# 3. Verificar integridade
sqlite3 dev.db "PRAGMA integrity_check;"

# 4. Reiniciar aplicação
pm2 start code2post

# 5. Validar funcionamento
curl http://localhost:3001/health
```

#### 3. Falha de Disco
**RTO:** 30 minutos | **RPO:** 24 horas
```bash
# 1. Montar novo disco
# 2. Restaurar backup completo
# 3. Reconfigurar aplicação
# 4. Testar funcionamento
# 5. Atualizar DNS se necessário
```

### Procedimentos de Backup

#### Backup Automático (Diário às 2:00 AM)
```bash
# Configurar crontab
0 2 * * * /path/to/code2post/scripts/backup-pre-deploy.sh

# Verificar execução
grep backup /var/log/cron
```

#### Backup Manual
```bash
# Executar backup
./scripts/backup-pre-deploy.sh

# Verificar backup
ls -la backups/
./scripts/validate-backup.sh [TIMESTAMP]
```

---

## 📞 Escalation

### Níveis de Escalation

#### Nível 1 - Suporte Técnico (0-30 min)
- **Responsabilidade:** Monitoramento básico, alertas de warning
- **Ações:** Health checks, reinicializações simples, limpeza de logs
- **Escalation:** Se não resolver em 30 minutos

#### Nível 2 - Engenheiro de Plantão (30-60 min)
- **Responsabilidade:** Problemas técnicos complexos, alertas críticos
- **Ações:** Troubleshooting avançado, rollbacks, análise de logs
- **Escalation:** Se não resolver em 60 minutos ou impacto alto

#### Nível 3 - Arquiteto/Lead (60+ min)
- **Responsabilidade:** Falhas arquiteturais, disaster recovery
- **Ações:** Decisões de arquitetura, mudanças emergenciais
- **Escalation:** Stakeholders de negócio se necessário

### Critérios de Escalation

#### Escalation Imediata
- Servidor completamente indisponível (>5 min)
- Perda de dados detectada
- Falha de segurança
- Taxa de erro >50%

#### Escalation em 30 min
- Taxa de erro >10%
- Latência >2000ms
- Circuit breakers críticos OPEN
- Falha de backup

#### Escalation em 60 min
- Performance degradada persistente
- Alertas de warning não resolvidos
- Problemas de capacidade

---

## 📱 Contatos de Emergência

### Time Técnico
- **Engenheiro de Plantão:** +55 11 99999-0001
- **Arquiteto de Sistema:** +55 11 99999-0002
- **DevOps Lead:** +55 11 99999-0003
- **DBA:** +55 11 99999-0004

### Stakeholders
- **CTO:** +55 11 99999-0010
- **Product Manager:** +55 11 99999-0011
- **Customer Success:** +55 11 99999-0012

### Fornecedores
- **Hosting Provider:** +55 11 99999-0020
- **Database Support:** +55 11 99999-0021
- **Monitoring Service:** +55 11 99999-0022

### Canais de Comunicação
- **Slack:** #code2post-alerts
- **Discord:** Code2Post Ops
- **Email:** <EMAIL>
- **PagerDuty:** code2post-production

---

## 📚 Recursos Adicionais

### Documentação
- [Arquitetura do Sistema](./ARCHITECTURE.md)
- [Guia de Deploy](./DEPLOY.md)
- [Troubleshooting Avançado](./TROUBLESHOOTING.md)
- [Plano de Disaster Recovery](./DISASTER-RECOVERY.md)

### Ferramentas
- **Monitoramento:** Grafana Dashboard
- **Logs:** ELK Stack / Splunk
- **Alertas:** PagerDuty / Slack
- **Métricas:** Prometheus

### Scripts Úteis
- `./scripts/health-check.sh` - Verificação completa de saúde
- `./scripts/performance-test.sh` - Teste de performance
- `./scripts/backup-validate.sh` - Validação de backup
- `./scripts/emergency-rollback.sh` - Rollback de emergência

---

**📝 Nota:** Este runbook deve ser atualizado sempre que houver mudanças na arquitetura, procedimentos ou contatos. Última revisão: 05/08/2025.
