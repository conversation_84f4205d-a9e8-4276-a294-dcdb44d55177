{"name": "backend", "type": "module", "version": "1.0.0", "main": "src/app.js", "scripts": {"start": "node src/app.js", "start:https": "node src/server.js", "dev": "nodemon src/app.js", "dev:https": "nodemon src/server.js", "start:prod": "NODE_ENV=production node src/server-production.js", "dev:prod": "NODE_ENV=production nodemon src/server-production.js", "start:letsencrypt": "NODE_ENV=production node src/server-production.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "code:check": "npm run lint && npm run format:check", "code:fix": "npm run lint:fix && npm run format", "migrate:postgresql": "node scripts/migrate-to-postgresql.js", "update:redis": "node scripts/update-redis.js", "validate:improvements": "node scripts/validate-improvements.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "@octokit/rest": "^22.0.0", "@prisma/client": "^6.13.0", "@types/pg": "^8.15.5", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csrf": "^3.1.0", "dotenv": "^17.2.0", "express": "^4.19.2", "express-jwt": "^8.5.1", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "greenlock-express": "^4.0.3", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "octokit": "^5.0.3", "pg": "^8.16.3", "prisma": "^6.13.0", "redis": "^5.6.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@jest/globals": "^30.0.5", "@types/jest": "^30.0.0", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "babel-jest": "^30.0.5", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.5", "jest-html-reporters": "^3.1.7", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.4"}}