/**
 * Configuração do Jest para testes de unidade e integração
 */

export default {
  // Ambiente de teste
  testEnvironment: 'node',
  
  // Suporte a ES modules
  preset: 'jest-esm-preset',
  extensionsToTreatAsEsm: ['.js'],
  globals: {
    'ts-jest': {
      useESM: true
    }
  },
  
  // Transformações
  transform: {
    '^.+\\.js$': ['babel-jest', { presets: [['@babel/preset-env', { targets: { node: 'current' } }]] }]
  },
  
  // Padrões de arquivos de teste
  testMatch: [
    '**/tests/unit/**/*.test.js',
    '**/tests/integration/**/*.test.js',
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  
  // Arquivos a ignorar
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/'
  ],
  
  // Configuração de cobertura
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/**/*.spec.js',
    '!src/app.js', // Arquivo principal
    '!**/node_modules/**'
  ],
  
  // Threshold de cobertura
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Setup de teste
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // Timeout para testes
  testTimeout: 30000,
  
  // Configurações de módulos
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // Variáveis de ambiente para testes
  setupFiles: ['<rootDir>/tests/env.js'],
  
  // Configuração de reporters
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './coverage',
      filename: 'test-report.html',
      expand: true
    }]
  ],
  
  // Configuração para testes paralelos
  maxWorkers: '50%',
  
  // Configuração de cache
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',
  
  // Configuração de verbose
  verbose: true,
  
  // Configuração de bail (parar no primeiro erro)
  bail: false,
  
  // Configuração de clear mocks
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true
};
