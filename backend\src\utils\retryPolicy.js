/**
 * Políticas de Retry para melhorar recuperação após falhas
 * Implementa diferentes estratégias de retry com backoff exponencial
 */

/**
 * Estratégias de backoff
 */
const BackoffStrategies = {
  FIXED: 'fixed',
  LINEAR: 'linear', 
  EXPONENTIAL: 'exponential',
  EXPONENTIAL_JITTER: 'exponential_jitter'
};

/**
 * Calcular delay baseado na estratégia
 */
const calculateDelay = (attempt, baseDelay, strategy, maxDelay = 30000) => {
  let delay;
  
  switch (strategy) {
    case BackoffStrategies.FIXED:
      delay = baseDelay;
      break;
      
    case BackoffStrategies.LINEAR:
      delay = baseDelay * attempt;
      break;
      
    case BackoffStrategies.EXPONENTIAL:
      delay = baseDelay * Math.pow(2, attempt - 1);
      break;
      
    case BackoffStrategies.EXPONENTIAL_JITTER:
      const exponential = baseDelay * Math.pow(2, attempt - 1);
      const jitter = Math.random() * 0.1 * exponential; // 10% jitter
      delay = exponential + jitter;
      break;
      
    default:
      delay = baseDelay;
  }
  
  return Math.min(delay, maxDelay);
};

/**
 * Verificar se erro é retryable
 */
const isRetryableError = (error, retryableErrors = []) => {
  // Erros de rede sempre retryable
  const networkErrors = [
    'ECONNREFUSED',
    'ENOTFOUND', 
    'ETIMEDOUT',
    'ECONNRESET',
    'EPIPE',
    'EHOSTUNREACH'
  ];
  
  // Erros HTTP retryable
  const retryableHttpCodes = [408, 429, 500, 502, 503, 504];
  
  // Erros do Prisma retryable
  const prismaErrors = [
    'P1001', // Can't reach database server
    'P1008', // Operations timed out
    'P1017'  // Server has closed the connection
  ];
  
  // Verificar código de erro
  if (networkErrors.includes(error.code)) return true;
  if (prismaErrors.includes(error.code)) return true;
  
  // Verificar status HTTP
  if (error.status && retryableHttpCodes.includes(error.status)) return true;
  if (error.response?.status && retryableHttpCodes.includes(error.response.status)) return true;
  
  // Verificar erros customizados
  return retryableErrors.some(retryableError => {
    if (typeof retryableError === 'string') {
      return error.message?.includes(retryableError);
    }
    if (retryableError instanceof RegExp) {
      return retryableError.test(error.message || '');
    }
    if (typeof retryableError === 'function') {
      return retryableError(error);
    }
    return false;
  });
};

/**
 * Classe principal de Retry Policy
 */
class RetryPolicy {
  constructor(options = {}) {
    this.maxAttempts = options.maxAttempts || 3;
    this.baseDelay = options.baseDelay || 1000;
    this.maxDelay = options.maxDelay || 30000;
    this.strategy = options.strategy || BackoffStrategies.EXPONENTIAL_JITTER;
    this.retryableErrors = options.retryableErrors || [];
    this.onRetry = options.onRetry || (() => {});
    this.onFailure = options.onFailure || (() => {});
    this.name = options.name || 'default';
    
    // Estatísticas
    this.stats = {
      totalAttempts: 0,
      successfulAttempts: 0,
      failedAttempts: 0,
      retriedAttempts: 0,
      averageAttempts: 0
    };
  }

  /**
   * Executar função com retry policy
   */
  async execute(fn, ...args) {
    let lastError;
    let totalAttempts = 0;
    
    for (let attempt = 1; attempt <= this.maxAttempts; attempt++) {
      totalAttempts++;
      this.stats.totalAttempts++;
      
      try {
        const result = await fn(...args);
        this.stats.successfulAttempts++;
        this.updateAverageAttempts(totalAttempts);
        
        if (attempt > 1) {
          console.log(`✅ Retry Policy '${this.name}': Sucesso na tentativa ${attempt}/${this.maxAttempts}`);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        // Verificar se deve fazer retry
        if (attempt === this.maxAttempts || !isRetryableError(error, this.retryableErrors)) {
          this.stats.failedAttempts++;
          this.updateAverageAttempts(totalAttempts);
          
          if (attempt === this.maxAttempts) {
            console.error(`❌ Retry Policy '${this.name}': Falha após ${this.maxAttempts} tentativas - ${error.message}`);
          } else {
            console.error(`❌ Retry Policy '${this.name}': Erro não retryable - ${error.message}`);
          }
          
          this.onFailure(error, attempt);
          throw error;
        }
        
        // Calcular delay para próxima tentativa
        const delay = calculateDelay(attempt, this.baseDelay, this.strategy, this.maxDelay);
        
        console.warn(`🔄 Retry Policy '${this.name}': Tentativa ${attempt}/${this.maxAttempts} falhou, retry em ${delay}ms - ${error.message}`);
        
        this.stats.retriedAttempts++;
        this.onRetry(error, attempt, delay);
        
        // Aguardar delay
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }

  /**
   * Atualizar média de tentativas
   */
  updateAverageAttempts(attempts) {
    const totalOperations = this.stats.successfulAttempts + this.stats.failedAttempts;
    this.stats.averageAttempts = this.stats.totalAttempts / totalOperations;
  }

  /**
   * Obter estatísticas
   */
  getStats() {
    const successRate = this.stats.totalAttempts > 0 ? 
      (this.stats.successfulAttempts / (this.stats.successfulAttempts + this.stats.failedAttempts) * 100).toFixed(2) + '%' : '0%';
    
    return {
      name: this.name,
      maxAttempts: this.maxAttempts,
      strategy: this.strategy,
      ...this.stats,
      successRate,
      retryRate: this.stats.totalAttempts > 0 ? 
        (this.stats.retriedAttempts / this.stats.totalAttempts * 100).toFixed(2) + '%' : '0%'
    };
  }

  /**
   * Resetar estatísticas
   */
  resetStats() {
    this.stats = {
      totalAttempts: 0,
      successfulAttempts: 0,
      failedAttempts: 0,
      retriedAttempts: 0,
      averageAttempts: 0
    };
  }
}

// Políticas pré-configuradas
const retryPolicies = {
  database: new RetryPolicy({
    name: 'database',
    maxAttempts: 3,
    baseDelay: 1000,
    strategy: BackoffStrategies.EXPONENTIAL_JITTER,
    retryableErrors: ['SQLITE_BUSY', 'SQLITE_LOCKED', 'database is locked']
  }),
  
  redis: new RetryPolicy({
    name: 'redis',
    maxAttempts: 5,
    baseDelay: 500,
    strategy: BackoffStrategies.EXPONENTIAL,
    retryableErrors: ['Redis connection', 'ECONNREFUSED']
  }),
  
  api: new RetryPolicy({
    name: 'api',
    maxAttempts: 3,
    baseDelay: 2000,
    strategy: BackoffStrategies.EXPONENTIAL_JITTER,
    retryableErrors: ['rate limit', 'timeout', 'network']
  }),
  
  auth: new RetryPolicy({
    name: 'auth',
    maxAttempts: 2,
    baseDelay: 1500,
    strategy: BackoffStrategies.FIXED,
    retryableErrors: ['timeout', 'connection']
  })
};

/**
 * Wrapper para funções com retry automático
 */
const withRetry = (policyName, fn) => {
  const policy = retryPolicies[policyName];
  if (!policy) {
    throw new Error(`Retry policy '${policyName}' não encontrada`);
  }
  
  return (...args) => policy.execute(fn, ...args);
};

/**
 * Obter estatísticas de todas as políticas
 */
const getAllRetryStats = () => {
  return Object.values(retryPolicies).map(policy => policy.getStats());
};

/**
 * Resetar todas as estatísticas
 */
const resetAllRetryStats = () => {
  Object.values(retryPolicies).forEach(policy => policy.resetStats());
  console.log('🔄 Estatísticas de retry resetadas');
};

export {
  RetryPolicy,
  BackoffStrategies,
  retryPolicies,
  withRetry,
  getAllRetryStats,
  resetAllRetryStats,
  isRetryableError,
  calculateDelay
};

export default retryPolicies;
