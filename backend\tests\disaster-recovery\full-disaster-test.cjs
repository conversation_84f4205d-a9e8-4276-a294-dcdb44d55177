/**
 * Teste Completo de Disaster Recovery
 * Simula falha total do servidor e valida recuperação
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

class DisasterRecoveryTester {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.backupDir = 'backups';
    this.testResults = {
      scenarios: [],
      summary: {},
      recommendations: []
    };
    
    this.scenarios = [
      {
        name: 'Crash Completo do Servidor',
        type: 'server_crash',
        description: 'Simula crash total do processo Node.js'
      },
      {
        name: 'Corrupção de Banco de Dados',
        type: 'database_corruption',
        description: 'Simula corrupção do arquivo de banco SQLite'
      },
      {
        name: '<PERSON>alha de Disco (Backup)',
        type: 'disk_failure',
        description: 'Simula falha de disco e restauração de backup'
      },
      {
        name: 'Falha de Dependências',
        type: 'dependency_failure',
        description: 'Simula falha de node_modules'
      },
      {
        name: 'Falha de Configuração',
        type: 'config_failure',
        description: 'Simula corrupção do arquivo .env'
      }
    ];
  }

  /**
   * Executar todos os testes de disaster recovery
   */
  async runAllTests() {
    console.log('🚨 TESTES DE DISASTER RECOVERY COMPLETO');
    console.log('======================================');
    console.log('');

    // Criar backup inicial
    await this.createInitialBackup();

    // Executar cenários
    for (const scenario of this.scenarios) {
      console.log(`🧪 Executando: ${scenario.name}`);
      console.log(`   📝 ${scenario.description}`);
      
      const result = await this.runScenario(scenario);
      this.testResults.scenarios.push(result);
      
      // Aguardar estabilização entre testes
      console.log('⏳ Aguardando estabilização...');
      await this.sleep(10000);
    }

    this.generateReport();
  }

  /**
   * Criar backup inicial para testes
   */
  async createInitialBackup() {
    console.log('💾 Criando backup inicial...');
    
    try {
      const timestamp = Date.now();
      const backupPath = path.join(this.backupDir, `disaster-test-${timestamp}`);
      
      // Criar diretório de backup
      if (!fs.existsSync(this.backupDir)) {
        fs.mkdirSync(this.backupDir, { recursive: true });
      }
      fs.mkdirSync(backupPath, { recursive: true });

      // Backup do banco de dados
      if (fs.existsSync('prisma/dev.db')) {
        fs.copyFileSync('prisma/dev.db', path.join(backupPath, 'dev.db.backup'));
      }

      // Backup das configurações
      if (fs.existsSync('.env')) {
        fs.copyFileSync('.env', path.join(backupPath, '.env.backup'));
      }

      // Backup do package.json
      if (fs.existsSync('package.json')) {
        fs.copyFileSync('package.json', path.join(backupPath, 'package.json.backup'));
      }

      this.initialBackupPath = backupPath;
      console.log(`   ✅ Backup criado em: ${backupPath}`);

    } catch (error) {
      console.error('   ❌ Erro ao criar backup:', error.message);
      throw error;
    }
  }

  /**
   * Executar cenário específico
   */
  async runScenario(scenario) {
    const startTime = Date.now();
    const result = {
      name: scenario.name,
      type: scenario.type,
      startTime: new Date(startTime),
      steps: [],
      success: false,
      recoveryTime: 0,
      errors: []
    };

    try {
      // Verificar estado inicial
      const initialState = await this.checkServerHealth();
      result.steps.push({
        step: 'initial_check',
        success: initialState.healthy,
        message: initialState.message,
        timestamp: new Date()
      });

      if (!initialState.healthy) {
        throw new Error('Servidor não está saudável no início do teste');
      }

      // Executar falha específica
      await this.simulateFailure(scenario, result);

      // Verificar que falha ocorreu
      const failureState = await this.checkServerHealth();
      result.steps.push({
        step: 'failure_verification',
        success: !failureState.healthy,
        message: failureState.message,
        timestamp: new Date()
      });

      // Executar recuperação
      await this.executeRecovery(scenario, result);

      // Verificar recuperação
      const recoveryState = await this.checkServerHealth();
      result.steps.push({
        step: 'recovery_verification',
        success: recoveryState.healthy,
        message: recoveryState.message,
        timestamp: new Date()
      });

      result.success = recoveryState.healthy;
      result.recoveryTime = Date.now() - startTime;

      console.log(`   ${result.success ? '✅' : '❌'} ${scenario.name}: ${result.recoveryTime}ms`);

    } catch (error) {
      result.errors.push({
        message: error.message,
        timestamp: new Date()
      });
      console.error(`   ❌ Erro no cenário: ${error.message}`);
    }

    return result;
  }

  /**
   * Simular falha específica
   */
  async simulateFailure(scenario, result) {
    console.log(`   🔥 Simulando falha: ${scenario.type}`);

    switch (scenario.type) {
      case 'server_crash':
        await this.simulateServerCrash(result);
        break;
      
      case 'database_corruption':
        await this.simulateDatabaseCorruption(result);
        break;
      
      case 'disk_failure':
        await this.simulateDiskFailure(result);
        break;
      
      case 'dependency_failure':
        await this.simulateDependencyFailure(result);
        break;
      
      case 'config_failure':
        await this.simulateConfigFailure(result);
        break;
      
      default:
        throw new Error(`Cenário não implementado: ${scenario.type}`);
    }
  }

  /**
   * Simular crash do servidor
   */
  async simulateServerCrash(result) {
    try {
      // Encontrar e matar processo Node.js
      await this.executeCommand('taskkill /F /IM node.exe', 'Matando processo Node.js');
      
      result.steps.push({
        step: 'server_crash_simulated',
        success: true,
        message: 'Processo Node.js terminado',
        timestamp: new Date()
      });

      await this.sleep(2000);

    } catch (error) {
      result.steps.push({
        step: 'server_crash_simulated',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Simular corrupção do banco
   */
  async simulateDatabaseCorruption(result) {
    try {
      const dbPath = 'prisma/dev.db';
      
      if (fs.existsSync(dbPath)) {
        // Corromper arquivo (escrever dados aleatórios)
        const corruptData = Buffer.from('CORRUPTED_DATABASE_FILE_FOR_TESTING');
        fs.writeFileSync(dbPath, corruptData);
        
        result.steps.push({
          step: 'database_corruption_simulated',
          success: true,
          message: 'Banco de dados corrompido',
          timestamp: new Date()
        });
      } else {
        throw new Error('Arquivo de banco não encontrado');
      }

    } catch (error) {
      result.steps.push({
        step: 'database_corruption_simulated',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Simular falha de disco
   */
  async simulateDiskFailure(result) {
    try {
      // Mover arquivos críticos para simular falha
      const criticalFiles = ['prisma/dev.db', '.env', 'package.json'];
      const tempDir = 'temp_disaster_test';
      
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir);
      }

      criticalFiles.forEach(file => {
        if (fs.existsSync(file)) {
          const fileName = path.basename(file);
          fs.renameSync(file, path.join(tempDir, fileName));
        }
      });

      result.steps.push({
        step: 'disk_failure_simulated',
        success: true,
        message: 'Arquivos críticos movidos (simulando falha de disco)',
        timestamp: new Date()
      });

    } catch (error) {
      result.steps.push({
        step: 'disk_failure_simulated',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Simular falha de dependências
   */
  async simulateDependencyFailure(result) {
    try {
      // Renomear node_modules para simular falha
      if (fs.existsSync('node_modules')) {
        fs.renameSync('node_modules', 'node_modules_backup_disaster_test');
        
        result.steps.push({
          step: 'dependency_failure_simulated',
          success: true,
          message: 'node_modules removido',
          timestamp: new Date()
        });
      } else {
        throw new Error('node_modules não encontrado');
      }

    } catch (error) {
      result.steps.push({
        step: 'dependency_failure_simulated',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Simular falha de configuração
   */
  async simulateConfigFailure(result) {
    try {
      // Corromper arquivo .env
      if (fs.existsSync('.env')) {
        fs.writeFileSync('.env', 'CORRUPTED_CONFIG_FILE_FOR_TESTING=true');
        
        result.steps.push({
          step: 'config_failure_simulated',
          success: true,
          message: 'Arquivo .env corrompido',
          timestamp: new Date()
        });
      } else {
        throw new Error('Arquivo .env não encontrado');
      }

    } catch (error) {
      result.steps.push({
        step: 'config_failure_simulated',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Executar recuperação
   */
  async executeRecovery(scenario, result) {
    console.log(`   🔧 Executando recuperação para: ${scenario.type}`);

    try {
      // Restaurar arquivos do backup
      await this.restoreFromBackup(result);
      
      // Reinstalar dependências se necessário
      if (scenario.type === 'dependency_failure') {
        await this.reinstallDependencies(result);
      }
      
      // Reiniciar servidor
      await this.restartServer(result);
      
      // Aguardar inicialização
      await this.sleep(5000);

    } catch (error) {
      result.errors.push({
        message: `Erro na recuperação: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  /**
   * Restaurar do backup
   */
  async restoreFromBackup(result) {
    try {
      if (!this.initialBackupPath) {
        throw new Error('Caminho de backup não definido');
      }

      // Restaurar banco de dados
      const dbBackup = path.join(this.initialBackupPath, 'dev.db.backup');
      if (fs.existsSync(dbBackup)) {
        fs.copyFileSync(dbBackup, 'prisma/dev.db');
      }

      // Restaurar configurações
      const envBackup = path.join(this.initialBackupPath, '.env.backup');
      if (fs.existsSync(envBackup)) {
        fs.copyFileSync(envBackup, '.env');
      }

      // Restaurar package.json
      const packageBackup = path.join(this.initialBackupPath, 'package.json.backup');
      if (fs.existsSync(packageBackup)) {
        fs.copyFileSync(packageBackup, 'package.json');
      }

      // Restaurar arquivos movidos (disk failure)
      const tempDir = 'temp_disaster_test';
      if (fs.existsSync(tempDir)) {
        const files = fs.readdirSync(tempDir);
        files.forEach(file => {
          const tempPath = path.join(tempDir, file);
          let targetPath = file;
          
          if (file === 'dev.db') {
            targetPath = 'prisma/dev.db';
          }
          
          fs.renameSync(tempPath, targetPath);
        });
        fs.rmdirSync(tempDir);
      }

      // Restaurar node_modules se foi movido
      if (fs.existsSync('node_modules_backup_disaster_test')) {
        fs.renameSync('node_modules_backup_disaster_test', 'node_modules');
      }

      result.steps.push({
        step: 'backup_restored',
        success: true,
        message: 'Arquivos restaurados do backup',
        timestamp: new Date()
      });

    } catch (error) {
      result.steps.push({
        step: 'backup_restored',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Reinstalar dependências
   */
  async reinstallDependencies(result) {
    try {
      await this.executeCommand('npm ci --production', 'Reinstalando dependências');
      
      result.steps.push({
        step: 'dependencies_reinstalled',
        success: true,
        message: 'Dependências reinstaladas',
        timestamp: new Date()
      });

    } catch (error) {
      result.steps.push({
        step: 'dependencies_reinstalled',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Reiniciar servidor
   */
  async restartServer(result) {
    try {
      // Tentar iniciar servidor em background
      const serverProcess = spawn('npm', ['run', 'dev'], {
        detached: true,
        stdio: 'ignore'
      });
      
      serverProcess.unref();
      
      result.steps.push({
        step: 'server_restarted',
        success: true,
        message: 'Servidor reiniciado',
        timestamp: new Date()
      });

    } catch (error) {
      result.steps.push({
        step: 'server_restarted',
        success: false,
        message: error.message,
        timestamp: new Date()
      });
    }
  }

  /**
   * Verificar saúde do servidor
   */
  async checkServerHealth() {
    try {
      const response = await this.makeRequest('/health', 'GET');
      
      return {
        healthy: response.status === 200,
        message: `HTTP ${response.status}`,
        response: response.data
      };

    } catch (error) {
      return {
        healthy: false,
        message: error.message,
        response: null
      };
    }
  }

  /**
   * Fazer requisição HTTP
   */
  async makeRequest(path, method = 'GET') {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: 'localhost',
        port: 3001,
        path,
        method,
        timeout: 5000
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          resolve({
            status: res.statusCode,
            data
          });
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }

  /**
   * Executar comando do sistema
   */
  async executeCommand(command, description) {
    return new Promise((resolve, reject) => {
      console.log(`   🔧 ${description}...`);
      
      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.error(`   ❌ ${description} falhou: ${error.message}`);
          reject(error);
        } else {
          console.log(`   ✅ ${description} concluído`);
          resolve(stdout);
        }
      });
    });
  }

  /**
   * Gerar relatório dos testes
   */
  generateReport() {
    console.log('');
    console.log('📊 RELATÓRIO DE DISASTER RECOVERY');
    console.log('=================================');

    let totalScenarios = this.testResults.scenarios.length;
    let successfulRecoveries = 0;
    let totalRecoveryTime = 0;

    this.testResults.scenarios.forEach(scenario => {
      const status = scenario.success ? '✅' : '❌';
      const recoveryTime = (scenario.recoveryTime / 1000).toFixed(1);
      
      console.log(`\n${status} ${scenario.name}:`);
      console.log(`   ⏱️ Tempo de recuperação: ${recoveryTime}s`);
      console.log(`   📋 Passos executados: ${scenario.steps.length}`);
      
      if (scenario.success) {
        successfulRecoveries++;
        totalRecoveryTime += scenario.recoveryTime;
      }
      
      if (scenario.errors.length > 0) {
        console.log(`   ❌ Erros: ${scenario.errors.length}`);
        scenario.errors.forEach(error => {
          console.log(`      - ${error.message}`);
        });
      }
    });

    // Resumo geral
    const successRate = (successfulRecoveries / totalScenarios * 100).toFixed(1);
    const avgRecoveryTime = successfulRecoveries > 0 ? 
      (totalRecoveryTime / successfulRecoveries / 1000).toFixed(1) : 'N/A';

    console.log(`\n📈 RESUMO GERAL:`);
    console.log(`   🎯 Taxa de recuperação: ${successRate}%`);
    console.log(`   ⏱️ Tempo médio de recuperação: ${avgRecoveryTime}s`);
    console.log(`   ✅ Cenários bem-sucedidos: ${successfulRecoveries}/${totalScenarios}`);

    // Recomendações
    this.generateRecommendations(successRate, avgRecoveryTime);

    // Salvar relatório
    this.saveReport();
  }

  /**
   * Gerar recomendações
   */
  generateRecommendations(successRate, avgRecoveryTime) {
    console.log(`\n💡 RECOMENDAÇÕES:`);
    
    if (successRate >= 90) {
      console.log('   ✅ Excelente capacidade de disaster recovery');
      console.log('   🚀 Sistema pronto para produção');
    } else if (successRate >= 70) {
      console.log('   ⚠️ Boa capacidade de recovery, mas pode melhorar');
      console.log('   🔧 Revisar procedimentos de backup e restauração');
    } else {
      console.log('   ❌ Capacidade de recovery insuficiente');
      console.log('   🚨 Implementar melhorias urgentes antes da produção');
    }

    if (parseFloat(avgRecoveryTime) > 300) { // 5 minutos
      console.log('   ⏰ Tempo de recuperação muito alto');
      console.log('   🔧 Automatizar mais processos de recuperação');
    }

    console.log('   📋 Próximos passos:');
    console.log('      - Automatizar backups regulares');
    console.log('      - Implementar monitoramento de saúde');
    console.log('      - Criar runbook de disaster recovery');
    console.log('      - Treinar equipe nos procedimentos');
  }

  /**
   * Salvar relatório
   */
  saveReport() {
    const reportPath = `disaster-recovery-report-${Date.now()}.json`;
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalScenarios: this.testResults.scenarios.length,
        successfulRecoveries: this.testResults.scenarios.filter(s => s.success).length,
        avgRecoveryTime: this.testResults.scenarios
          .filter(s => s.success)
          .reduce((sum, s) => sum + s.recoveryTime, 0) / 
          this.testResults.scenarios.filter(s => s.success).length
      },
      scenarios: this.testResults.scenarios
    };

    try {
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n📁 Relatório salvo: ${reportPath}`);
    } catch (error) {
      console.error(`❌ Erro ao salvar relatório: ${error.message}`);
    }
  }

  /**
   * Utilitário para sleep
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const tester = new DisasterRecoveryTester();
  tester.runAllTests().catch(console.error);
}

module.exports = { DisasterRecoveryTester };
