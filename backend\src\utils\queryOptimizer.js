/**
 * Otimizador de Queries Prisma
 * Implementa select específico e otimizações para reduzir payload
 */

/**
 * Campos essenciais para diferentes contextos
 */
export const SELECT_FIELDS = {
  // Usuário para listagens públicas
  userPublic: {
    id: true,
    name: true,
    email: true,
    isActive: true,
    createdAt: true
  },

  // Usuário para perfil próprio
  userProfile: {
    id: true,
    name: true,
    email: true,
    isActive: true,
    emailVerified: true,
    lastLoginAt: true,
    createdAt: true,
    updatedAt: true,
    preferences: true
  },

  // Usuário para autenticação (inclui senha)
  userAuth: {
    id: true,
    email: true,
    password: true,
    isActive: true,
    emailVerified: true,
    lastLoginAt: true
  },

  // Post para listagens
  postList: {
    id: true,
    title: true,
    content: true,
    status: true,
    platform: true,
    createdAt: true,
    updatedAt: true,
    userId: true,
    user: {
      select: {
        id: true,
        name: true
      }
    }
  },

  // Post completo
  postFull: {
    id: true,
    title: true,
    content: true,
    status: true,
    platform: true,
    scheduledFor: true,
    publishedAt: true,
    createdAt: true,
    updatedAt: true,
    userId: true,
    user: {
      select: {
        id: true,
        name: true,
        email: true
      }
    }
  },

  // GitHub Config básico
  githubConfigBasic: {
    id: true,
    isConnected: true,
    username: true,
    createdAt: true,
    updatedAt: true
  },

  // GitHub Config completo
  githubConfigFull: {
    id: true,
    githubId: true,
    username: true,
    email: true,
    name: true,
    avatarUrl: true,
    accessToken: true,
    refreshToken: true,
    isConnected: true,
    createdAt: true,
    updatedAt: true
  }
};

/**
 * Queries otimizadas para diferentes casos de uso
 */
export class QueryOptimizer {
  /**
   * Buscar usuários com paginação otimizada
   */
  static async findUsers(prisma, options = {}) {
    const {
      page = 1,
      limit = 10,
      search = '',
      isActive = true,
      includeInactive = false,
      select = SELECT_FIELDS.userPublic
    } = options;

    const skip = (page - 1) * limit;
    const where = {
      ...(includeInactive ? {} : { isActive }),
      ...(search ? {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      } : {})
    };

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Buscar posts com paginação e filtros otimizados
   */
  static async findPosts(prisma, options = {}) {
    const {
      page = 1,
      limit = 10,
      userId = null,
      status = null,
      platform = null,
      search = '',
      select = SELECT_FIELDS.postList,
      orderBy = { createdAt: 'desc' }
    } = options;

    const skip = (page - 1) * limit;
    const where = {
      ...(userId ? { userId } : {}),
      ...(status ? { status } : {}),
      ...(platform ? { platform } : {}),
      ...(search ? {
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } }
        ]
      } : {})
    };

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        select,
        skip,
        take: limit,
        orderBy
      }),
      prisma.post.count({ where })
    ]);

    return {
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Buscar post por ID com otimização
   */
  static async findPostById(prisma, id, options = {}) {
    const {
      userId = null,
      select = SELECT_FIELDS.postFull
    } = options;

    const where = {
      id,
      ...(userId ? { userId } : {})
    };

    return await prisma.post.findUnique({
      where,
      select
    });
  }

  /**
   * Buscar usuário por email para autenticação
   */
  static async findUserForAuth(prisma, email) {
    return await prisma.user.findUnique({
      where: { 
        email: email.toLowerCase(),
        isActive: true 
      },
      select: SELECT_FIELDS.userAuth
    });
  }

  /**
   * Buscar perfil do usuário
   */
  static async findUserProfile(prisma, userId) {
    return await prisma.user.findUnique({
      where: { 
        id: userId,
        isActive: true 
      },
      select: SELECT_FIELDS.userProfile
    });
  }

  /**
   * Buscar configuração GitHub do usuário
   */
  static async findGitHubConfig(prisma, userId, includeTokens = false) {
    const select = includeTokens ? 
      SELECT_FIELDS.githubConfigFull : 
      SELECT_FIELDS.githubConfigBasic;

    return await prisma.gitHubConfig.findUnique({
      where: { userId },
      select
    });
  }

  /**
   * Estatísticas do usuário otimizadas
   */
  static async getUserStats(prisma, userId) {
    const [
      totalPosts,
      publishedPosts,
      draftPosts,
      scheduledPosts,
      recentPosts
    ] = await Promise.all([
      prisma.post.count({
        where: { userId }
      }),
      prisma.post.count({
        where: { userId, status: 'PUBLISHED' }
      }),
      prisma.post.count({
        where: { userId, status: 'DRAFT' }
      }),
      prisma.post.count({
        where: { userId, status: 'SCHEDULED' }
      }),
      prisma.post.findMany({
        where: { userId },
        select: {
          id: true,
          title: true,
          status: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        take: 5
      })
    ]);

    return {
      totalPosts,
      publishedPosts,
      draftPosts,
      scheduledPosts,
      recentPosts
    };
  }

  /**
   * Busca otimizada para dashboard
   */
  static async getDashboardData(prisma, userId) {
    const [
      userProfile,
      userStats,
      githubConfig,
      recentPosts
    ] = await Promise.all([
      this.findUserProfile(prisma, userId),
      this.getUserStats(prisma, userId),
      this.findGitHubConfig(prisma, userId, false),
      this.findPosts(prisma, {
        userId,
        limit: 5,
        select: SELECT_FIELDS.postList
      })
    ]);

    return {
      user: userProfile,
      stats: userStats,
      github: githubConfig,
      recentPosts: recentPosts.posts
    };
  }

  /**
   * Busca em lote otimizada
   */
  static async findPostsByIds(prisma, ids, options = {}) {
    const {
      userId = null,
      select = SELECT_FIELDS.postList
    } = options;

    const where = {
      id: { in: ids },
      ...(userId ? { userId } : {})
    };

    return await prisma.post.findMany({
      where,
      select,
      orderBy: { createdAt: 'desc' }
    });
  }

  /**
   * Contadores otimizados para analytics
   */
  static async getAnalytics(prisma, options = {}) {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 dias atrás
      endDate = new Date(),
      userId = null
    } = options;

    const baseWhere = {
      createdAt: {
        gte: startDate,
        lte: endDate
      },
      ...(userId ? { userId } : {})
    };

    const [
      totalUsers,
      activeUsers,
      totalPosts,
      publishedPosts,
      postsPerDay
    ] = await Promise.all([
      // Total de usuários no período
      prisma.user.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      }),

      // Usuários ativos (com posts)
      prisma.user.count({
        where: {
          isActive: true,
          posts: {
            some: baseWhere
          }
        }
      }),

      // Total de posts
      prisma.post.count({
        where: baseWhere
      }),

      // Posts publicados
      prisma.post.count({
        where: {
          ...baseWhere,
          status: 'PUBLISHED'
        }
      }),

      // Posts por dia (últimos 7 dias)
      prisma.post.groupBy({
        by: ['createdAt'],
        where: {
          ...baseWhere,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        _count: {
          id: true
        }
      })
    ]);

    return {
      totalUsers,
      activeUsers,
      totalPosts,
      publishedPosts,
      publishRate: totalPosts > 0 ? (publishedPosts / totalPosts * 100).toFixed(1) : '0',
      postsPerDay: postsPerDay.length
    };
  }
}

/**
 * Middleware para logging de queries lentas
 */
export const queryLogger = {
  /**
   * Log de query com tempo de execução
   */
  async logQuery(queryName, queryFn, threshold = 500) {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      
      if (duration > threshold) {
        console.warn(`🐌 Query lenta detectada: ${queryName} (${duration}ms)`);
      } else {
        console.log(`⚡ Query: ${queryName} (${duration}ms)`);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Query falhou: ${queryName} (${duration}ms) - ${error.message}`);
      throw error;
    }
  }
};

export default QueryOptimizer;
