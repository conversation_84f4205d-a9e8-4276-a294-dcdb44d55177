{"testSuite": "Resilience Tests", "startTime": "2025-08-05T02:00:34.169Z", "endTime": "2025-08-05T02:25:00.249Z", "duration": 1466080, "totalTests": 14, "passed": 14, "failed": 0, "critical": 0, "warnings": 0, "results": [{"phase": "Load", "test": "Carga Baixa", "status": "SUCCESS", "metrics": {"duration": 62.259, "totalRequests": 174, "successfulRequests": 100, "failedRequests": 74, "avgRPS": 2.79, "errorRate": 42.53, "avgLatency": 1228, "minLatency": 1, "maxLatency": 6008, "p50": 531, "p95": 4129, "p99": 5988, "throughput": 2.79, "concurrency": 0}, "timestamp": "2025-08-05T02:01:36.437Z"}, {"phase": "Load", "test": "Carga Média", "status": "SUCCESS", "metrics": {"duration": 122.931, "totalRequests": 2472, "successfulRequests": 4, "failedRequests": 2564, "avgRPS": 20.11, "errorRate": 103.72, "avgLatency": 129, "minLatency": 0, "maxLatency": 9455, "p50": 2, "p95": 75, "p99": 4752, "throughput": 20.11, "concurrency": -96}, "timestamp": "2025-08-05T02:04:09.383Z"}, {"phase": "Load", "test": "Carga Alta", "status": "SUCCESS", "metrics": {"duration": 182.791, "totalRequests": 8576, "successfulRequests": 104, "failedRequests": 8568, "avgRPS": 46.92, "errorRate": 99.91, "avgLatency": 161, "minLatency": 0, "maxLatency": 9847, "p50": 1, "p95": 184, "p99": 4852, "throughput": 46.92, "concurrency": -96}, "timestamp": "2025-08-05T02:07:42.179Z"}, {"phase": "Load", "test": "Carga Pico", "status": "SUCCESS", "metrics": {"duration": 302.794, "totalRequests": 31570, "successfulRequests": 200, "failedRequests": 31370, "avgRPS": 104.26, "errorRate": 99.37, "avgLatency": 51, "minLatency": 0, "maxLatency": 7271, "p50": 1, "p95": 9, "p99": 1827, "throughput": 104.26, "concurrency": 0}, "timestamp": "2025-08-05T02:13:14.995Z"}, {"phase": "Stress", "test": "Stress CPU", "status": "SUCCESS", "metrics": {"type": "CPU Stress", "totalRequests": 51000, "successfulRequests": 41148, "failedRequests": 9852, "errorRate": 19.31764705882353, "avgCPU": 59760912.28070176, "duration": 60453, "systemMetrics": {"avgCPU": 59760912.28070176, "maxCPU": 86749000, "avgMemory": 20.18731475294682, "maxMemory": 32.176109313964844, "samples": 57}}, "timestamp": "2025-08-05T02:14:45.542Z"}, {"phase": "Stress", "test": "<PERSON><PERSON>", "status": "SUCCESS", "metrics": {"type": "Memory Stress", "totalRequests": 700, "successfulRequests": 700, "failedRequests": 0, "memoryPeak": 532.3239059448242, "memoryAvg": 271.8796558380127, "duration": 45016, "systemMetrics": {"avgCPU": 87528386.36363636, "maxCPU": 88468000, "avgMemory": 271.8796558380127, "maxMemory": 532.3239059448242, "samples": 44}}, "timestamp": "2025-08-05T02:16:30.644Z"}, {"phase": "Stress", "test": "<PERSON><PERSON>", "status": "SUCCESS", "metrics": {"type": "Connection Stress", "maxConnections": 1000, "successfulConnections": 1000, "failedConnections": 0, "connectionSuccessRate": 100, "duration": 30471, "systemMetrics": {"avgCPU": 89481333.33333333, "maxCPU": 89484000, "avgMemory": 10.611003875732422, "maxMemory": 10.619590759277344, "samples": 30}}, "timestamp": "2025-08-05T02:18:01.122Z"}, {"phase": "Stress", "test": "Stress Banco de Dados", "status": "SUCCESS", "metrics": {"type": "Database Stress", "totalQueries": 23800, "successfulQueries": 23800, "failedQueries": 0, "querySuccessRate": 100, "duration": 60233, "systemMetrics": {"avgCPU": 91946186.44067797, "maxCPU": 93859000, "avgMemory": 13.249934697555283, "maxMemory": 20.545028686523438, "samples": 59}}, "timestamp": "2025-08-05T02:20:01.368Z"}, {"phase": "Failure", "test": "Desconexão Banco", "status": "SUCCESS", "metrics": {"type": "Database Failure Simulation", "successBefore": 5, "successAfter": 4, "recovered": true}, "timestamp": "2025-08-05T02:21:18.577Z"}, {"phase": "Failure", "test": "Redis Indisponível", "status": "SUCCESS", "metrics": {"type": "Redis Failure Simulation", "cacheHitsBefore": 10, "cacheHitsAfter": 10, "recovered": true}, "timestamp": "2025-08-05T02:22:14.907Z"}, {"phase": "Failure", "test": "Sobrecarga de Requests", "status": "SUCCESS", "metrics": {"type": "Request Flood", "totalRequests": 200, "successful": 200, "failed": 0, "duration": 295, "survivedFlood": true}, "timestamp": "2025-08-05T02:23:00.214Z"}, {"phase": "Failure", "test": "Corrupção de Dados", "status": "SUCCESS", "metrics": {"type": "Data Corruption Simulation", "validRequests": 5, "invalidRequests": 0, "systemStable": true}, "timestamp": "2025-08-05T02:23:45.571Z"}, {"phase": "Recovery", "test": "Auto Recovery", "status": "SUCCESS", "metrics": {"recoveryTime": 15426, "status": "failed", "error": "Timeout"}, "timestamp": "2025-08-05T02:24:46.008Z"}, {"phase": "Memory", "test": "Memory Leak Detection", "status": "SUCCESS", "metrics": {"initialMemory": 10, "finalMemory": 9, "memoryGrowth": -1, "avgMemoryPerRequest": -10, "leakDetected": false, "snapshots": 99}, "timestamp": "2025-08-05T02:25:00.249Z"}]}