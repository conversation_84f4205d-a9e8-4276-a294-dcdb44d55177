/**
 * Teste do Sistema de Métricas de Uso
 */

// Importar serviço
import('./src/services/usageMetricsService.js').then(async (module) => {
  const usageMetrics = module.default;
  
  console.log('🧪 TESTANDO SISTEMA DE MÉTRICAS DE USO');
  console.log('=====================================');
  
  try {
    // Teste 1: Registrar uso básico
    console.log('\n1️⃣ Registrando uso básico...');
    await usageMetrics.recordUsage('user123', '/api/posts', 'GET', { responseTime: 150 });
    await usageMetrics.recordUsage('user123', '/api/posts', 'POST', { responseTime: 300 });
    await usageMetrics.recordUsage('user456', '/health', 'GET', { responseTime: 50 });
    console.log('   ✅ 3 registros de uso adicionados');
    
    // Teste 2: Registrar uso em lote
    console.log('\n2️⃣ Registrando uso em lote...');
    const promises = [];
    for (let i = 0; i < 20; i++) {
      promises.push(usageMetrics.recordUsage(
        `user${i % 5}`, 
        `/api/endpoint${i % 3}`, 
        'GET', 
        { responseTime: Math.random() * 500 + 100 }
      ));
    }
    await Promise.all(promises);
    console.log('   ✅ 20 registros de uso em lote adicionados');
    
    // Aguardar flush do buffer
    console.log('\n⏳ Aguardando flush do buffer...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Teste 3: Obter métricas de usuário
    console.log('\n3️⃣ Obtendo métricas de usuário...');
    const userMetrics = await usageMetrics.getUserUsageMetrics('user123', 'daily');
    if (userMetrics) {
      console.log('   📊 Métricas do user123:');
      Object.entries(userMetrics).forEach(([key, value]) => {
        console.log(`      ${key}: ${value} requests`);
      });
    } else {
      console.log('   ⚠️ Métricas não disponíveis (Redis pode estar indisponível)');
    }
    
    // Teste 4: Analisar padrões de uso
    console.log('\n4️⃣ Analisando padrões de uso...');
    const analysis = await usageMetrics.analyzeUsagePatterns('user123');
    if (analysis) {
      console.log('   📈 Análise de padrões:');
      console.log(`      Uso médio diário: ${analysis.patterns.avgDailyUsage.toFixed(1)}`);
      console.log(`      Uso máximo diário: ${analysis.patterns.maxDailyUsage}`);
      console.log(`      Tendência: ${analysis.patterns.trend}`);
      
      if (analysis.recommendations.limits) {
        console.log('   💡 Recomendações de rate limiting:');
        console.log(`      Limite diário: ${analysis.recommendations.limits.daily}`);
        console.log(`      Limite horário: ${analysis.recommendations.limits.hourly}`);
        console.log(`      Burst limit: ${analysis.recommendations.limits.burst}`);
        console.log(`      Confiança: ${(analysis.recommendations.limits.confidence * 100).toFixed(1)}%`);
      }
      
      if (analysis.recommendations.suggestions.length > 0) {
        console.log('   📝 Sugestões:');
        analysis.recommendations.suggestions.forEach(suggestion => {
          console.log(`      - ${suggestion}`);
        });
      }
    } else {
      console.log('   ⚠️ Análise não disponível (Redis pode estar indisponível)');
    }
    
    // Teste 5: Estatísticas globais
    console.log('\n5️⃣ Obtendo estatísticas globais...');
    const globalStats = await usageMetrics.getGlobalStats();
    if (globalStats) {
      console.log('   🌍 Estatísticas globais:');
      console.log(`      Total de requests: ${globalStats.totalRequests}`);
      console.log(`      Usuários únicos: ${globalStats.uniqueUsers}`);
      console.log(`      Média por usuário: ${globalStats.avgRequestsPerUser}`);
      console.log(`      Timestamp: ${new Date(globalStats.timestamp).toLocaleTimeString()}`);
    } else {
      console.log('   ⚠️ Estatísticas globais não disponíveis');
    }
    
    // Teste 6: Teste de performance
    console.log('\n6️⃣ Testando performance do sistema...');
    const startTime = Date.now();
    const performancePromises = [];
    
    for (let i = 0; i < 100; i++) {
      performancePromises.push(usageMetrics.recordUsage(
        `perfuser${i % 10}`, 
        `/perf/endpoint${i % 5}`, 
        'GET', 
        { responseTime: Math.random() * 200 + 50 }
      ));
    }
    
    await Promise.all(performancePromises);
    const endTime = Date.now();
    
    console.log(`   ⚡ 100 registros processados em ${endTime - startTime}ms`);
    console.log(`   📊 Taxa: ${(100 / (endTime - startTime) * 1000).toFixed(1)} registros/segundo`);
    
    console.log('\n✅ TODOS OS TESTES DE MÉTRICAS CONCLUÍDOS!');
    console.log('==========================================');
    
  } catch (error) {
    console.error('❌ Erro nos testes de métricas:', error.message);
  }
}).catch(console.error);
