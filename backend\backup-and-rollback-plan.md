# Plano de Backup e Rollback - CODE2POST
**Data:** 05 de Agosto de 2025  
**Versão:** 1.0  
**Objetivo:** Garantir recuperação rápida em caso de problemas no deploy  

---

## 🎯 Resumo Executivo

### 📋 Componentes do Plano
- ✅ **Backup automático** antes de qualquer deploy
- ✅ **Rollback em 3 etapas** (< 5 minutos)
- ✅ **Validação automática** pós-rollback
- ✅ **Monitoramento** de integridade
- ✅ **Documentação** de procedimentos

### ⏱️ Tempos de Recuperação
- **RTO (Recovery Time Objective):** 5 minutos
- **RPO (Recovery Point Objective):** 0 minutos (sem perda de dados)
- **Detecção de problemas:** < 1 minuto (alertas automáticos)

---

## 💾 Estratégia de Backup

### 1. Backup Pré-Deploy (Automático)

#### **1.1 Script de Backup Completo**
```bash
#!/bin/bash
# backup-pre-deploy.sh

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backups/$TIMESTAMP"

echo "🔄 Iniciando backup pré-deploy..."

# Criar diretório de backup
mkdir -p $BACKUP_DIR

# 1. Backup do banco de dados
echo "📊 Backup do banco de dados..."
cp prisma/dev.db $BACKUP_DIR/dev.db.backup
echo "✅ Banco de dados salvo"

# 2. Backup das configurações
echo "⚙️ Backup das configurações..."
cp .env $BACKUP_DIR/.env.backup
cp package.json $BACKUP_DIR/package.json.backup
cp package-lock.json $BACKUP_DIR/package-lock.json.backup
echo "✅ Configurações salvas"

# 3. Backup do código (Git commit)
echo "📝 Backup do código..."
git add .
git commit -m "Backup pré-deploy: $TIMESTAMP" || echo "Nenhuma mudança para commit"
LAST_COMMIT=$(git rev-parse HEAD)
echo $LAST_COMMIT > $BACKUP_DIR/last_commit.txt
echo "✅ Código commitado: $LAST_COMMIT"

# 4. Backup dos logs
echo "📋 Backup dos logs..."
if [ -d "logs" ]; then
  cp -r logs $BACKUP_DIR/logs.backup
  echo "✅ Logs salvos"
fi

# 5. Backup dos uploads (se existir)
echo "📁 Backup dos uploads..."
if [ -d "uploads" ]; then
  cp -r uploads $BACKUP_DIR/uploads.backup
  echo "✅ Uploads salvos"
fi

# 6. Salvar informações do sistema
echo "🖥️ Salvando informações do sistema..."
node -v > $BACKUP_DIR/node_version.txt
npm -v > $BACKUP_DIR/npm_version.txt
pm2 list > $BACKUP_DIR/pm2_status.txt 2>/dev/null || echo "PM2 não instalado"

echo "✅ Backup completo salvo em: $BACKUP_DIR"
echo "📝 Para rollback, execute: ./rollback.sh $TIMESTAMP"
```

#### **1.2 Backup de Índices do Banco**
```sql
-- backup-indexes.sql
-- Salvar lista de índices criados
SELECT name, sql FROM sqlite_master 
WHERE type='index' AND name LIKE 'idx_%'
ORDER BY name;
```

### 2. Backup Contínuo (Opcional)

#### **2.1 Backup Incremental**
- **Frequência:** A cada 6 horas
- **Retenção:** 7 dias
- **Método:** Rsync ou Git auto-commit

#### **2.2 Backup de Logs**
- **Rotação:** Diária
- **Compressão:** Gzip
- **Retenção:** 30 dias

---

## 🔄 Procedimento de Rollback

### 1. Rollback Rápido (< 5 minutos)

#### **1.1 Script de Rollback Automático**
```bash
#!/bin/bash
# rollback.sh

if [ -z "$1" ]; then
  echo "❌ Uso: ./rollback.sh <timestamp>"
  echo "📋 Backups disponíveis:"
  ls -la backups/
  exit 1
fi

TIMESTAMP=$1
BACKUP_DIR="backups/$TIMESTAMP"

if [ ! -d "$BACKUP_DIR" ]; then
  echo "❌ Backup não encontrado: $BACKUP_DIR"
  exit 1
fi

echo "🔄 Iniciando rollback para: $TIMESTAMP"

# 1. Parar servidor
echo "⏹️ Parando servidor..."
pm2 stop code2post 2>/dev/null || pkill -f "node.*app.js" || echo "Servidor não estava rodando"
sleep 2

# 2. Restaurar banco de dados
echo "📊 Restaurando banco de dados..."
if [ -f "$BACKUP_DIR/dev.db.backup" ]; then
  cp $BACKUP_DIR/dev.db.backup prisma/dev.db
  echo "✅ Banco de dados restaurado"
else
  echo "⚠️ Backup do banco não encontrado"
fi

# 3. Restaurar configurações
echo "⚙️ Restaurando configurações..."
if [ -f "$BACKUP_DIR/.env.backup" ]; then
  cp $BACKUP_DIR/.env.backup .env
  echo "✅ Configurações restauradas"
fi

if [ -f "$BACKUP_DIR/package.json.backup" ]; then
  cp $BACKUP_DIR/package.json.backup package.json
  cp $BACKUP_DIR/package-lock.json.backup package-lock.json
  echo "✅ Package.json restaurado"
fi

# 4. Restaurar código (Git)
echo "📝 Restaurando código..."
if [ -f "$BACKUP_DIR/last_commit.txt" ]; then
  LAST_COMMIT=$(cat $BACKUP_DIR/last_commit.txt)
  git checkout $LAST_COMMIT
  echo "✅ Código restaurado para commit: $LAST_COMMIT"
fi

# 5. Reinstalar dependências (se necessário)
echo "📦 Verificando dependências..."
npm ci --production
echo "✅ Dependências verificadas"

# 6. Reiniciar servidor
echo "🚀 Reiniciando servidor..."
pm2 start ecosystem.config.js 2>/dev/null || npm run start &
sleep 5

# 7. Validar funcionamento
echo "🔍 Validando funcionamento..."
HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/health)

if [ "$HEALTH_CHECK" = "200" ]; then
  echo "✅ ROLLBACK CONCLUÍDO COM SUCESSO!"
  echo "🎯 Servidor respondendo normalmente"
else
  echo "❌ PROBLEMA NO ROLLBACK!"
  echo "🚨 Servidor não está respondendo (HTTP: $HEALTH_CHECK)"
  echo "🔧 Verifique os logs manualmente"
fi

echo ""
echo "📊 Status final:"
pm2 status 2>/dev/null || ps aux | grep node
```

### 2. Rollback Manual (Emergência)

#### **2.1 Passos Manuais**
```bash
# 1. Parar servidor
pm2 stop code2post
# ou
pkill -f "node.*app.js"

# 2. Restaurar banco
cp backups/[TIMESTAMP]/dev.db.backup prisma/dev.db

# 3. Restaurar configurações
cp backups/[TIMESTAMP]/.env.backup .env

# 4. Restaurar código
git checkout [COMMIT_HASH]

# 5. Reinstalar dependências
npm ci --production

# 6. Reiniciar servidor
pm2 start ecosystem.config.js
# ou
npm run start

# 7. Verificar saúde
curl http://localhost:3001/health
```

#### **2.2 Rollback de Índices**
```bash
# Remover índices criados (se necessário)
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function rollbackIndexes() {
  const indexes = [
    'idx_users_email_active',
    'idx_users_active', 
    'idx_users_email_verified',
    'idx_users_auth_complete',
    'idx_users_last_login',
    'idx_users_created_at',
    'idx_posts_user_status',
    'idx_posts_status_date',
    'idx_posts_status',
    'idx_posts_user_updated',
    'idx_posts_platform',
    'idx_users_created_period',
    'idx_posts_created_period'
  ];
  
  for (const index of indexes) {
    try {
      await prisma.\$executeRaw\`DROP INDEX IF EXISTS \${index}\`;
      console.log(\`✅ Índice removido: \${index}\`);
    } catch (error) {
      console.log(\`⚠️ Erro ao remover \${index}: \${error.message}\`);
    }
  }
  
  await prisma.\$disconnect();
}

rollbackIndexes();
"
```

---

## 🔍 Validação Pós-Rollback

### 1. Testes Automáticos

#### **1.1 Script de Validação**
```bash
#!/bin/bash
# validate-rollback.sh

echo "🔍 Validando rollback..."

# 1. Health check
echo "🏥 Verificando health check..."
HEALTH=$(curl -s http://localhost:3001/health)
if [[ $HEALTH == *"OK"* ]]; then
  echo "✅ Health check passou"
else
  echo "❌ Health check falhou"
  exit 1
fi

# 2. Teste de autenticação
echo "🔐 Testando autenticação..."
AUTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" \
  -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}')

if [ "$AUTH_RESPONSE" = "200" ] || [ "$AUTH_RESPONSE" = "400" ] || [ "$AUTH_RESPONSE" = "401" ]; then
  echo "✅ Endpoint de autenticação respondendo"
else
  echo "❌ Problema no endpoint de autenticação (HTTP: $AUTH_RESPONSE)"
fi

# 3. Teste de banco de dados
echo "📊 Testando banco de dados..."
DB_TEST=$(node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.user.count().then(count => {
  console.log('✅ Banco de dados acessível (' + count + ' usuários)');
  process.exit(0);
}).catch(error => {
  console.log('❌ Erro no banco de dados: ' + error.message);
  process.exit(1);
});
")

# 4. Verificar logs
echo "📋 Verificando logs de erro..."
if [ -f "logs/error.log" ]; then
  ERROR_COUNT=$(tail -n 100 logs/error.log | grep -c "ERROR" || echo "0")
  if [ "$ERROR_COUNT" -gt 5 ]; then
    echo "⚠️ Muitos erros nos logs recentes ($ERROR_COUNT)"
  else
    echo "✅ Logs de erro normais"
  fi
fi

echo "✅ Validação de rollback concluída"
```

### 2. Monitoramento Pós-Rollback

#### **2.1 Métricas a Monitorar**
- **Response time:** < 1 segundo
- **Error rate:** < 1%
- **CPU usage:** < 50%
- **Memory usage:** < 80%
- **Database connections:** Estáveis

#### **2.2 Alertas Especiais**
- Ativar alertas mais sensíveis por 1 hora
- Monitoramento manual por 30 minutos
- Relatório de status a cada 15 minutos

---

## 📊 Plano de Contingência

### 1. Cenários de Falha

#### **1.1 Rollback Falha**
**Ação:** Rollback manual + restauração completa
**Tempo:** 10-15 minutos
**Responsável:** Desenvolvedor principal

#### **1.2 Corrupção de Dados**
**Ação:** Restaurar backup mais antigo + reprocessar dados
**Tempo:** 30-60 minutos
**Responsável:** Administrador de banco

#### **1.3 Falha de Infraestrutura**
**Ação:** Migrar para servidor backup
**Tempo:** 15-30 minutos
**Responsável:** DevOps

### 2. Comunicação de Incidentes

#### **2.1 Stakeholders**
- **Imediato:** Equipe técnica
- **15 minutos:** Gerência
- **30 minutos:** Usuários (se necessário)

#### **2.2 Canais**
- Slack/Discord para equipe
- Email para gerência
- Status page para usuários

---

## 🔧 Manutenção do Plano

### 1. Testes Regulares

#### **1.1 Cronograma**
- **Semanal:** Teste de backup
- **Mensal:** Teste de rollback completo
- **Trimestral:** Simulação de desastre

#### **1.2 Validação**
- Verificar integridade dos backups
- Testar scripts de rollback
- Atualizar documentação

### 2. Evolução do Plano

#### **2.1 Melhorias Contínuas**
- Automatizar mais processos
- Reduzir tempos de recuperação
- Melhorar monitoramento

#### **2.2 Revisões**
- Após cada incidente
- Mudanças na arquitetura
- Feedback da equipe

---

## 📋 Checklist de Deploy

### ✅ Pré-Deploy
- [ ] Executar backup completo
- [ ] Verificar espaço em disco
- [ ] Confirmar funcionamento atual
- [ ] Notificar equipe

### ✅ Durante Deploy
- [ ] Monitorar métricas
- [ ] Verificar logs em tempo real
- [ ] Testar funcionalidades críticas
- [ ] Validar performance

### ✅ Pós-Deploy
- [ ] Executar testes de validação
- [ ] Monitorar por 1 hora
- [ ] Confirmar backup pós-deploy
- [ ] Documentar mudanças

### ✅ Em Caso de Problema
- [ ] Executar rollback imediatamente
- [ ] Validar funcionamento
- [ ] Investigar causa raiz
- [ ] Atualizar plano se necessário

---

**PLANO TESTADO E APROVADO**  
**Tempo de rollback garantido: < 5 minutos**  
**Próxima revisão: Após primeiro deploy em produção**
