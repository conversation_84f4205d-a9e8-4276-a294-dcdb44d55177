-- Índices de Performance para suportar 10+ usuários simultâneos
-- Criado em: 2025-08-05
-- Objetivo: <PERSON><PERSON><PERSON><PERSON> queries mais frequentes identificadas nos testes de resiliência

-- 1. ÍNDICES PARA TABELA USERS (mais crítica para autenticação)

-- Índice composto para login (email + isActive)
-- Otimiza: SELECT * FROM users WHERE email = ? AND isActive = true
CREATE INDEX IF NOT EXISTS idx_users_email_active ON users(email, isActive);

-- Índice para busca por ID (já existe como PRIMARY, mas garantindo)
-- Otimiza: SELECT * FROM users WHERE id = ?
-- (<PERSON><PERSON> co<PERSON> pela PRIMARY KEY, mas documentando)

-- Índice para usuários ativos
-- Otimiza: SELECT * FROM users WHERE isActive = true
CREATE INDEX IF NOT EXISTS idx_users_active ON users(isActive);

-- Índice para email verificado
-- Otimiza: SELECT * FROM users WHERE emailVerified = true
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(emailVerified);

-- Índice composto para autenticação completa
-- Otimiza: SELECT * FROM users WHERE email = ? AND isActive = true AND emailVerified = true
CREATE INDEX IF NOT EXISTS idx_users_auth_complete ON users(email, isActive, emailVerified);

-- Índice para último login (para analytics)
-- Otimiza: SELECT * FROM users ORDER BY lastLoginAt DESC
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(lastLoginAt);

-- Índice para data de criação (para analytics)
-- Otimiza: SELECT * FROM users WHERE createdAt >= ?
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(createdAt);

-- 2. ÍNDICES PARA TABELA POSTS (segunda mais importante)

-- Índice composto para posts do usuário
-- Otimiza: SELECT * FROM posts WHERE userId = ? AND isPublished = true
CREATE INDEX IF NOT EXISTS idx_posts_user_published ON posts(userId, isPublished);

-- Índice para posts publicados ordenados por data
-- Otimiza: SELECT * FROM posts WHERE isPublished = true ORDER BY createdAt DESC
CREATE INDEX IF NOT EXISTS idx_posts_published_date ON posts(isPublished, createdAt);

-- Índice para busca por slug
-- Otimiza: SELECT * FROM posts WHERE slug = ?
CREATE INDEX IF NOT EXISTS idx_posts_slug ON posts(slug);

-- Índice para posts por status
-- Otimiza: SELECT * FROM posts WHERE status = ?
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);

-- Índice composto para listagem de posts do usuário
-- Otimiza: SELECT * FROM posts WHERE userId = ? ORDER BY updatedAt DESC
CREATE INDEX IF NOT EXISTS idx_posts_user_updated ON posts(userId, updatedAt);

-- 3. ÍNDICES PARA TABELA SESSIONS (se existir - para cache de sessões)

-- Nota: Se implementarmos cache de sessões no banco, adicionar:
-- CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(userId);
-- CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expiresAt);
-- CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token);

-- 4. ÍNDICES PARA PERFORMANCE DE JOINS

-- Se houver relacionamentos frequentes, otimizar:
-- Exemplo: posts com informações do usuário
-- (Já coberto pelos índices acima)

-- 5. ÍNDICES PARA ANALYTICS E RELATÓRIOS

-- Índice para contagem de usuários por período
-- Otimiza: SELECT COUNT(*) FROM users WHERE createdAt BETWEEN ? AND ?
CREATE INDEX IF NOT EXISTS idx_users_created_period ON users(createdAt, isActive);

-- Índice para contagem de posts por período
-- Otimiza: SELECT COUNT(*) FROM posts WHERE createdAt BETWEEN ? AND ?
CREATE INDEX IF NOT EXISTS idx_posts_created_period ON posts(createdAt, isPublished);

-- 6. ÍNDICES PARA BUSCA E FILTROS

-- Índice para busca por nome de usuário (se implementado)
-- CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);

-- Índice para busca em posts por título (se implementado)
-- CREATE INDEX IF NOT EXISTS idx_posts_title ON posts(title);

-- 7. ESTATÍSTICAS E COMENTÁRIOS

-- Verificar estatísticas dos índices (SQLite)
-- PRAGMA index_info(idx_users_email_active);
-- PRAGMA index_list(users);

-- Para PostgreSQL (produção), usar:
-- ANALYZE users;
-- ANALYZE posts;

-- Monitorar performance:
-- EXPLAIN QUERY PLAN SELECT * FROM users WHERE email = ? AND isActive = true;

-- 8. MANUTENÇÃO DOS ÍNDICES

-- SQLite: Os índices são mantidos automaticamente
-- PostgreSQL: Considerar REINDEX periódico em produção

-- 9. NOTAS DE PERFORMANCE

-- Estes índices foram criados baseados nos testes de resiliência que identificaram:
-- 1. Queries de autenticação como gargalo principal
-- 2. Necessidade de suportar 10+ usuários simultâneos
-- 3. Latência alta (596ms) que pode ser reduzida com índices adequados

-- Impacto esperado:
-- - Redução de 50-70% na latência de autenticação
-- - Suporte a 15-20 usuários simultâneos
-- - Melhoria na taxa de sucesso de 62% para 85%+ com 10 usuários

-- Monitoramento recomendado:
-- - Verificar tamanho dos índices: .schema em SQLite
-- - Monitorar query plans: EXPLAIN QUERY PLAN
-- - Acompanhar métricas de performance após deploy
