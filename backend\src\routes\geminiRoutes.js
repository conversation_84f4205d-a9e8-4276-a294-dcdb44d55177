import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { validateGitHubToken } from '../middleware/githubAuth.js';
import {
  generateLinkedInPost,
  generateTechnicalSummary,
  generateHashtags,
} from '../services/geminiService.js';
import { getCommits } from '../services/githubService.js';
import rateLimit from 'express-rate-limit';

const router = express.Router();

// Rate limiting para rotas da Gemini API
const geminiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: process.env.NODE_ENV === 'test' ? 50 : 10, // 50 para testes, 10 para produção
  message: {
    error: 'Muitas requisições para a API de IA. Tente novamente em 15 minutos.',
    error_en: 'Too many requests to AI API. Try again in 15 minutes.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Aplicar rate limiting em todas as rotas
router.use(geminiLimiter);

/**
 * @route POST /api/gemini/generate-post
 * @desc Gera um post para LinkedIn baseado nos commits do GitHub
 * @access Private
 */
router.post('/generate-post', authenticateToken, async (req, res) => {
  try {
    const { repoName, branch = 'main', limit = 10 } = req.body;
    const userId = req.user.userId;

    if (!repoName) {
      return res.status(400).json({
        error: 'Nome do repositório é obrigatório',
        error_en: 'Repository name is required',
      });
    }

    // Buscar commits do GitHub
    const commits = await getCommits(userId, repoName, branch, limit);

    if (!commits || commits.length === 0) {
      return res.status(404).json({
        error: 'Nenhum commit encontrado para este repositório',
        error_en: 'No commits found for this repository',
      });
    }

    // Informações do usuário para o prompt
    const userInfo = {
      login: req.user.username,
      repoName,
    };

    // Gerar post com Gemini
    const generatedPost = await generateLinkedInPost(commits, userInfo);

    // Gerar hashtags
    const hashtags = await generateHashtags(commits);

    res.json({
      success: true,
      data: {
        post: generatedPost,
        hashtags,
        commitsAnalyzed: commits.length,
        repository: repoName,
        branch,
      },
      message: 'Post gerado com sucesso',
      message_en: 'Post generated successfully',
    });
  } catch (error) {
    console.error('Erro ao gerar post:', error);
    res.status(500).json({
      error: 'Erro interno do servidor ao gerar post',
      error_en: 'Internal server error while generating post',
    });
  }
});

/**
 * @route POST /api/gemini/generate-summary
 * @desc Gera um resumo técnico dos commits
 * @access Private
 */
router.post('/generate-summary', authenticateToken, async (req, res) => {
  try {
    const { repoName, branch = 'main', limit = 20 } = req.body;
    const userId = req.user.userId;

    if (!repoName) {
      return res.status(400).json({
        error: 'Nome do repositório é obrigatório',
        error_en: 'Repository name is required',
      });
    }

    // Buscar commits do GitHub
    const commits = await getCommits(userId, repoName, branch, limit);

    if (!commits || commits.length === 0) {
      return res.status(404).json({
        error: 'Nenhum commit encontrado para este repositório',
        error_en: 'No commits found for this repository',
      });
    }

    // Gerar resumo técnico
    const technicalSummary = await generateTechnicalSummary(commits);

    res.json({
      success: true,
      data: {
        summary: technicalSummary,
        commitsAnalyzed: commits.length,
        repository: repoName,
        branch,
      },
      message: 'Resumo técnico gerado com sucesso',
      message_en: 'Technical summary generated successfully',
    });
  } catch (error) {
    console.error('Erro ao gerar resumo:', error);
    res.status(500).json({
      error: 'Erro interno do servidor ao gerar resumo',
      error_en: 'Internal server error while generating summary',
    });
  }
});

/**
 * @route POST /api/gemini/generate-hashtags
 * @desc Gera hashtags relevantes baseadas nos commits
 * @access Private
 */
router.post('/generate-hashtags', authenticateToken, async (req, res) => {
  try {
    const { repoName, branch = 'main', limit = 15 } = req.body;
    const userId = req.user.userId;

    if (!repoName) {
      return res.status(400).json({
        error: 'Nome do repositório é obrigatório',
        error_en: 'Repository name is required',
      });
    }

    // Buscar commits do GitHub
    const commits = await getCommits(userId, repoName, branch, limit);

    if (!commits || commits.length === 0) {
      return res.status(404).json({
        error: 'Nenhum commit encontrado para este repositório',
        error_en: 'No commits found for this repository',
      });
    }

    // Gerar hashtags
    const hashtags = await generateHashtags(commits);

    res.json({
      success: true,
      data: {
        hashtags,
        commitsAnalyzed: commits.length,
        repository: repoName,
        branch,
      },
      message: 'Hashtags geradas com sucesso',
      message_en: 'Hashtags generated successfully',
    });
  } catch (error) {
    console.error('Erro ao gerar hashtags:', error);
    res.status(500).json({
      error: 'Erro interno do servidor ao gerar hashtags',
      error_en: 'Internal server error while generating hashtags',
    });
  }
});

/**
 * @route POST /api/gemini/analyze-commits
 * @desc Análise completa: post, resumo e hashtags
 * @access Private
 */
router.post('/analyze-commits', authenticateToken, async (req, res) => {
  try {
    const { repoName, branch = 'main', limit = 15 } = req.body;
    const userId = req.user.userId;

    if (!repoName) {
      return res.status(400).json({
        error: 'Nome do repositório é obrigatório',
        error_en: 'Repository name is required',
      });
    }

    // Buscar commits do GitHub
    const commits = await getCommits(userId, repoName, branch, limit);

    if (!commits || commits.length === 0) {
      return res.status(404).json({
        error: 'Nenhum commit encontrado para este repositório',
        error_en: 'No commits found for this repository',
      });
    }

    // Informações do usuário
    const userInfo = {
      login: req.user.username,
      repoName,
    };

    // Executar todas as análises em paralelo
    const [generatedPost, technicalSummary, hashtags] = await Promise.all([
      generateLinkedInPost(commits, userInfo),
      generateTechnicalSummary(commits),
      generateHashtags(commits),
    ]);

    res.json({
      success: true,
      data: {
        post: generatedPost,
        summary: technicalSummary,
        hashtags,
        commitsAnalyzed: commits.length,
        repository: repoName,
        branch,
        analysisDate: new Date().toISOString(),
      },
      message: 'Análise completa realizada com sucesso',
      message_en: 'Complete analysis performed successfully',
    });
  } catch (error) {
    console.error('Erro na análise completa:', error);
    res.status(500).json({
      error: 'Erro interno do servidor na análise',
      error_en: 'Internal server error during analysis',
    });
  }
});

export default router;
