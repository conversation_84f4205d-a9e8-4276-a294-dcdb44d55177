/**
 * Script de debug para identificar EXATAMENTE onde está o rate limiting
 */

const http = require('http');

async function debugRateLimit() {
  console.log('🔍 DEBUG COMPLETO DO RATE LIMITING');
  console.log('==================================');
  
  // Teste 1: Verificar se o servidor está rodando
  console.log('1️⃣ Verificando se servidor está rodando...');
  try {
    const healthResult = await makeRequest('/health', 'GET');
    console.log(`   ✅ Servidor rodando: ${healthResult.status}`);
  } catch (error) {
    console.log(`   ❌ Servidor não está rodando: ${error.message}`);
    console.log(`   ❌ Stack: ${error.stack}`);
    return;
  }
  
  // Teste 2: Testar rota raiz
  console.log('2️⃣ Testando rota raiz /...');
  try {
    const rootResult = await makeRequest('/', 'GET');
    console.log(`   Status: ${rootResult.status}`);
    console.log(`   Headers: ${JSON.stringify(rootResult.headers)}`);
  } catch (error) {
    console.log(`   Erro: ${error.message}`);
  }
  
  // Teste 3: Testar /auth/login (rota que está falhando)
  console.log('3️⃣ Testando /auth/login...');
  try {
    const loginResult = await makeRequest('/auth/login', 'POST', {
      email: '<EMAIL>',
      password: 'LoadTest123!'
    });
    console.log(`   Status: ${loginResult.status}`);
    console.log(`   Headers: ${JSON.stringify(loginResult.headers)}`);
    console.log(`   Body: ${loginResult.data}`);
  } catch (error) {
    console.log(`   Erro: ${error.message}`);
  }
  
  // Teste 4: Testar rota de login direta (se existir)
  console.log('4️⃣ Testando /login...');
  try {
    const directLoginResult = await makeRequest('/login', 'POST', {
      email: '<EMAIL>',
      password: 'LoadTest123!'
    });
    console.log(`   Status: ${directLoginResult.status}`);
    console.log(`   Headers: ${JSON.stringify(directLoginResult.headers)}`);
  } catch (error) {
    console.log(`   Erro: ${error.message}`);
  }
  
  // Teste 5: Testar com diferentes User-Agents
  console.log('5️⃣ Testando com diferentes User-Agents...');
  const userAgents = ['Mozilla/5.0', 'LoadTest', 'curl/7.68.0', 'PostmanRuntime'];
  
  for (const ua of userAgents) {
    try {
      const result = await makeRequest('/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'LoadTest123!'
      }, ua);
      console.log(`   ${ua}: Status ${result.status}`);
    } catch (error) {
      console.log(`   ${ua}: Erro - ${error.message}`);
    }
  }
  
  // Teste 6: Verificar se há middleware interceptando
  console.log('6️⃣ Testando endpoints que NÃO deveriam ter rate limiting...');
  const testEndpoints = ['/health', '/csrf-token', '/'];
  
  for (const endpoint of testEndpoints) {
    try {
      const result = await makeRequest(endpoint, 'GET');
      console.log(`   ${endpoint}: Status ${result.status}`);
      if (result.status === 429) {
        console.log(`   🚨 RATE LIMITING TAMBÉM EM ${endpoint}!`);
      }
    } catch (error) {
      console.log(`   ${endpoint}: Erro - ${error.message}`);
    }
  }
}

async function makeRequest(path, method = 'GET', data = null, userAgent = 'DebugScript') {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : '';
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path,
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': userAgent
      },
      timeout: 10000
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => { responseData += chunk; });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: responseData,
          headers: {
            'x-ratelimit-limit': res.headers['x-ratelimit-limit'],
            'x-ratelimit-remaining': res.headers['x-ratelimit-remaining'],
            'x-ratelimit-reset': res.headers['x-ratelimit-reset'],
            'server': res.headers['server'],
            'content-type': res.headers['content-type']
          }
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(postData);
    }
    
    req.end();
  });
}

debugRateLimit().catch(console.error);
