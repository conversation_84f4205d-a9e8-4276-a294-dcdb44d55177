/**
 * Script de limpeza de arquivos temporários
 * Remove arquivos de teste, logs antigos, builds e outros temporários
 */

const fs = require('fs');
const path = require('path');

function removeIfExists(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        fs.rmSync(filePath, { recursive: true, force: true });
        console.log(`✅ Diretório removido: ${filePath}`);
      } else {
        fs.unlinkSync(filePath);
        console.log(`✅ Arquivo removido: ${filePath}`);
      }
      return true;
    }
    return false;
  } catch (error) {
    console.log(`⚠️ Erro ao remover ${filePath}: ${error.message}`);
    return false;
  }
}

function cleanupTempFiles() {
  console.log('🧹 Iniciando limpeza de arquivos temporários...');
  console.log('');

  // Arquivos temporários comuns
  const tempFiles = [
    // Dumps e caches
    'dump.rdb',
    'coverage',
    
    // Arquivos de teste temporários
    'test-*.cjs',
    'temp-*.js',
    'temp-*.cjs',
    
    // Builds antigos
    '../frontend/dist',
    
    // Logs antigos (manter apenas os últimos 3 dias)
    // será implementado separadamente
  ];

  let removedCount = 0;

  // Remover arquivos específicos
  tempFiles.forEach(pattern => {
    if (pattern.includes('*')) {
      // Padrão com wildcard - implementar se necessário
      return;
    }
    
    const fullPath = path.resolve(__dirname, '..', pattern);
    if (removeIfExists(fullPath)) {
      removedCount++;
    }
  });

  // Limpar logs antigos (manter apenas últimos 3 dias)
  const logsDir = path.join(__dirname, '../logs');
  if (fs.existsSync(logsDir)) {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    
    const logFiles = fs.readdirSync(logsDir);
    logFiles.forEach(file => {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime < threeDaysAgo) {
        if (removeIfExists(filePath)) {
          removedCount++;
        }
      }
    });
  }

  // Limpar node_modules/.cache se existir
  const cacheDir = path.join(__dirname, '../node_modules/.cache');
  if (removeIfExists(cacheDir)) {
    removedCount++;
  }

  console.log('');
  console.log(`🎉 Limpeza concluída! ${removedCount} itens removidos.`);
  
  if (removedCount === 0) {
    console.log('✨ Nenhum arquivo temporário encontrado - projeto já está limpo!');
  }
}

function listTempFiles() {
  console.log('🔍 Listando arquivos temporários encontrados...');
  console.log('');

  const tempPatterns = [
    'dump.rdb',
    'coverage',
    'test-*.cjs',
    'temp-*.js',
    'temp-*.cjs',
    '../frontend/dist',
    'node_modules/.cache'
  ];

  let foundCount = 0;

  tempPatterns.forEach(pattern => {
    if (pattern.includes('*')) {
      return; // Skip wildcards for now
    }
    
    const fullPath = path.resolve(__dirname, '..', pattern);
    if (fs.existsSync(fullPath)) {
      const stats = fs.statSync(fullPath);
      const type = stats.isDirectory() ? 'DIR' : 'FILE';
      const size = stats.isDirectory() ? '' : ` (${(stats.size / 1024).toFixed(1)}KB)`;
      console.log(`📁 ${type}: ${pattern}${size}`);
      foundCount++;
    }
  });

  // Listar logs antigos
  const logsDir = path.join(__dirname, '../logs');
  if (fs.existsSync(logsDir)) {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    
    const logFiles = fs.readdirSync(logsDir);
    logFiles.forEach(file => {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime < threeDaysAgo) {
        console.log(`📄 LOG ANTIGO: logs/${file} (${stats.mtime.toDateString()})`);
        foundCount++;
      }
    });
  }

  console.log('');
  if (foundCount === 0) {
    console.log('✨ Nenhum arquivo temporário encontrado!');
  } else {
    console.log(`📋 Total encontrado: ${foundCount} itens`);
    console.log('💡 Execute com "cleanup" para remover estes arquivos');
  }
}

// Executar baseado no comando
const command = process.argv[2];

switch (command) {
  case 'cleanup':
  case 'clean':
    cleanupTempFiles();
    break;
  case 'list':
  case 'ls':
    listTempFiles();
    break;
  default:
    console.log('🧹 Script de Limpeza de Arquivos Temporários');
    console.log('');
    console.log('📋 Comandos disponíveis:');
    console.log('  node cleanup-temp-files.cjs list    - Listar arquivos temporários');
    console.log('  node cleanup-temp-files.cjs cleanup - Remover arquivos temporários');
    console.log('');
    console.log('💡 Recomendação: Execute "list" primeiro para ver o que será removido');
    break;
}
